!function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/usr/src/project/dist",n(n.s=46)}([function(e,t,n){"use strict";n.d(t,"f",(function(){return o})),n.d(t,"g",(function(){return r})),n.d(t,"b",(function(){return c})),n.d(t,"a",(function(){return s})),n.d(t,"d",(function(){return l})),n.d(t,"c",(function(){return d})),n.d(t,"e",(function(){return u}));
/**
 * @license
 * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */
const o=`{{lit-${String(Math.random()).slice(2)}}}`,r=`\x3c!--${o}--\x3e`,i=new RegExp(`${o}|${r}`),c="$lit$";class s{constructor(e,t){this.parts=[],this.element=t;const n=[],r=[],s=document.createTreeWalker(t.content,133,null,!1);let l=0,f=-1,p=0;const{strings:b,values:{length:h}}=e;for(;p<h;){const e=s.nextNode();if(null!==e){if(f++,1===e.nodeType){if(e.hasAttributes()){const t=e.attributes,{length:n}=t;let o=0;for(let e=0;e<n;e++)a(t[e].name,c)&&o++;for(;o-- >0;){const t=b[p],n=u.exec(t)[2],o=n.toLowerCase()+c,r=e.getAttribute(o);e.removeAttribute(o);const s=r.split(i);this.parts.push({type:"attribute",index:f,name:n,strings:s}),p+=s.length-1}}"TEMPLATE"===e.tagName&&(r.push(e),s.currentNode=e.content)}else if(3===e.nodeType){const t=e.data;if(t.indexOf(o)>=0){const o=e.parentNode,r=t.split(i),s=r.length-1;for(let t=0;t<s;t++){let n,i=r[t];if(""===i)n=d();else{const e=u.exec(i);null!==e&&a(e[2],c)&&(i=i.slice(0,e.index)+e[1]+e[2].slice(0,-c.length)+e[3]),n=document.createTextNode(i)}o.insertBefore(n,e),this.parts.push({type:"node",index:++f})}""===r[s]?(o.insertBefore(d(),e),n.push(e)):e.data=r[s],p+=s}}else if(8===e.nodeType)if(e.data===o){const t=e.parentNode;null!==e.previousSibling&&f!==l||(f++,t.insertBefore(d(),e)),l=f,this.parts.push({type:"node",index:f}),null===e.nextSibling?e.data="":(n.push(e),f--),p++}else{let t=-1;for(;-1!==(t=e.data.indexOf(o,t+1));)this.parts.push({type:"node",index:-1}),p++}}else s.currentNode=r.pop()}for(const e of n)e.parentNode.removeChild(e)}}const a=(e,t)=>{const n=e.length-t.length;return n>=0&&e.slice(n)===t},l=e=>-1!==e.index,d=()=>document.createComment(""),u=/([ \x09\x0a\x0c\x0d])([^\0-\x1F\x7F-\x9F "'>=/]+)([ \x09\x0a\x0c\x0d]*=[ \x09\x0a\x0c\x0d]*(?:[^ \x09\x0a\x0c\x0d"'`<>=]*|"[^"]*|'[^']*))$/},function(e,t,n){"use strict";n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return f})),n.d(t,"e",(function(){return p})),n.d(t,"c",(function(){return b})),n.d(t,"f",(function(){return h})),n.d(t,"d",(function(){return g}));var o=n(8),r=n(4),i=n(2),c=n(10),s=n(9),a=n(0);
/**
 * @license
 * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */
const l=e=>null===e||!("object"==typeof e||"function"==typeof e),d=e=>Array.isArray(e)||!(!e||!e[Symbol.iterator]);class u{constructor(e,t,n){this.dirty=!0,this.element=e,this.name=t,this.strings=n,this.parts=[];for(let e=0;e<n.length-1;e++)this.parts[e]=this._createPart()}_createPart(){return new f(this)}_getValue(){const e=this.strings,t=e.length-1,n=this.parts;if(1===t&&""===e[0]&&""===e[1]){const e=n[0].value;if("symbol"==typeof e)return String(e);if("string"==typeof e||!d(e))return e}let o="";for(let r=0;r<t;r++){o+=e[r];const t=n[r];if(void 0!==t){const e=t.value;if(l(e)||!d(e))o+="string"==typeof e?e:String(e);else for(const t of e)o+="string"==typeof t?t:String(t)}}return o+=e[t],o}commit(){this.dirty&&(this.dirty=!1,this.element.setAttribute(this.name,this._getValue()))}}class f{constructor(e){this.value=void 0,this.committer=e}setValue(e){e===i.a||l(e)&&e===this.value||(this.value=e,Object(o.b)(e)||(this.committer.dirty=!0))}commit(){for(;Object(o.b)(this.value);){const e=this.value;this.value=i.a,e(this)}this.value!==i.a&&this.committer.commit()}}class p{constructor(e){this.value=void 0,this.__pendingValue=void 0,this.options=e}appendInto(e){this.startNode=e.appendChild(Object(a.c)()),this.endNode=e.appendChild(Object(a.c)())}insertAfterNode(e){this.startNode=e,this.endNode=e.nextSibling}appendIntoPart(e){e.__insert(this.startNode=Object(a.c)()),e.__insert(this.endNode=Object(a.c)())}insertAfterPart(e){e.__insert(this.startNode=Object(a.c)()),this.endNode=e.endNode,e.endNode=this.startNode}setValue(e){this.__pendingValue=e}commit(){if(null===this.startNode.parentNode)return;for(;Object(o.b)(this.__pendingValue);){const e=this.__pendingValue;this.__pendingValue=i.a,e(this)}const e=this.__pendingValue;e!==i.a&&(l(e)?e!==this.value&&this.__commitText(e):e instanceof s.b?this.__commitTemplateResult(e):e instanceof Node?this.__commitNode(e):d(e)?this.__commitIterable(e):e===i.b?(this.value=i.b,this.clear()):this.__commitText(e))}__insert(e){this.endNode.parentNode.insertBefore(e,this.endNode)}__commitNode(e){this.value!==e&&(this.clear(),this.__insert(e),this.value=e)}__commitText(e){const t=this.startNode.nextSibling,n="string"==typeof(e=null==e?"":e)?e:String(e);t===this.endNode.previousSibling&&3===t.nodeType?t.data=n:this.__commitNode(document.createTextNode(n)),this.value=e}__commitTemplateResult(e){const t=this.options.templateFactory(e);if(this.value instanceof c.a&&this.value.template===t)this.value.update(e.values);else{const n=new c.a(t,e.processor,this.options),o=n._clone();n.update(e.values),this.__commitNode(o),this.value=n}}__commitIterable(e){Array.isArray(this.value)||(this.value=[],this.clear());const t=this.value;let n,o=0;for(const r of e)n=t[o],void 0===n&&(n=new p(this.options),t.push(n),0===o?n.appendIntoPart(this):n.insertAfterPart(t[o-1])),n.setValue(r),n.commit(),o++;o<t.length&&(t.length=o,this.clear(n&&n.endNode))}clear(e=this.startNode){Object(r.b)(this.startNode.parentNode,e.nextSibling,this.endNode)}}class b{constructor(e,t,n){if(this.value=void 0,this.__pendingValue=void 0,2!==n.length||""!==n[0]||""!==n[1])throw new Error("Boolean attributes can only contain a single expression");this.element=e,this.name=t,this.strings=n}setValue(e){this.__pendingValue=e}commit(){for(;Object(o.b)(this.__pendingValue);){const e=this.__pendingValue;this.__pendingValue=i.a,e(this)}if(this.__pendingValue===i.a)return;const e=!!this.__pendingValue;this.value!==e&&(e?this.element.setAttribute(this.name,""):this.element.removeAttribute(this.name),this.value=e),this.__pendingValue=i.a}}class h extends u{constructor(e,t,n){super(e,t,n),this.single=2===n.length&&""===n[0]&&""===n[1]}_createPart(){return new m(this)}_getValue(){return this.single?this.parts[0].value:super._getValue()}commit(){this.dirty&&(this.dirty=!1,this.element[this.name]=this._getValue())}}class m extends f{}let v=!1;(()=>{try{const e={get capture(){return v=!0,!1}};window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch(e){}})();class g{constructor(e,t,n){this.value=void 0,this.__pendingValue=void 0,this.element=e,this.eventName=t,this.eventContext=n,this.__boundHandleEvent=e=>this.handleEvent(e)}setValue(e){this.__pendingValue=e}commit(){for(;Object(o.b)(this.__pendingValue);){const e=this.__pendingValue;this.__pendingValue=i.a,e(this)}if(this.__pendingValue===i.a)return;const e=this.__pendingValue,t=this.value,n=null==e||null!=t&&(e.capture!==t.capture||e.once!==t.once||e.passive!==t.passive),r=null!=e&&(null==t||n);n&&this.element.removeEventListener(this.eventName,this.__boundHandleEvent,this.__options),r&&(this.__options=x(e),this.element.addEventListener(this.eventName,this.__boundHandleEvent,this.__options)),this.value=e,this.__pendingValue=i.a}handleEvent(e){"function"==typeof this.value?this.value.call(this.eventContext||this.element,e):this.value.handleEvent(e)}}const x=e=>e&&(v?{capture:e.capture,passive:e.passive,once:e.once}:e.capture)},function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return r}));
/**
 * @license
 * Copyright (c) 2018 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */
const o={},r={}},function(e,t,n){"use strict";n.d(t,"c",(function(){return u.c})),n.d(t,"b",(function(){return z})),n.d(t,"a",(function(){return P}));var o=n(4),r=n(0);function i(e,t){const{element:{content:n},parts:o}=e,r=document.createTreeWalker(n,133,null,!1);let i=s(o),c=o[i],a=-1,l=0;const d=[];let u=null;for(;r.nextNode();){a++;const e=r.currentNode;for(e.previousSibling===u&&(u=null),t.has(e)&&(d.push(e),null===u&&(u=e)),null!==u&&l++;void 0!==c&&c.index===a;)c.index=null!==u?-1:c.index-l,i=s(o,i),c=o[i]}d.forEach(e=>e.parentNode.removeChild(e))}const c=e=>{let t=11===e.nodeType?0:1;const n=document.createTreeWalker(e,133,null,!1);for(;n.nextNode();)t++;return t},s=(e,t=-1)=>{for(let n=t+1;n<e.length;n++){const t=e[n];if(Object(r.d)(t))return n}return-1};var a=n(7),l=n(6),d=n(10),u=n(5);
/**
 * @license
 * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */
const f=(e,t)=>`${e}--${t}`;let p=!0;void 0===window.ShadyCSS?p=!1:void 0===window.ShadyCSS.prepareTemplateDom&&(console.warn("Incompatible ShadyCSS version detected. Please update to at least @webcomponents/webcomponentsjs@2.0.2 and @webcomponents/shadycss@1.3.1."),p=!1);const b=e=>t=>{const n=f(t.type,e);let o=l.a.get(n);void 0===o&&(o={stringsArray:new WeakMap,keyString:new Map},l.a.set(n,o));let i=o.stringsArray.get(t.strings);if(void 0!==i)return i;const c=t.strings.join(r.f);if(i=o.keyString.get(c),void 0===i){const n=t.getTemplateElement();p&&window.ShadyCSS.prepareTemplateDom(n,e),i=new r.a(t,n),o.keyString.set(c,i)}return o.stringsArray.set(t.strings,i),i},h=["html","svg"],m=new Set,v=(e,t,n)=>{m.add(e);const o=n?n.element:document.createElement("template"),r=t.querySelectorAll("style"),{length:a}=r;if(0===a)return void window.ShadyCSS.prepareTemplateStyles(o,e);const d=document.createElement("style");for(let e=0;e<a;e++){const t=r[e];t.parentNode.removeChild(t),d.textContent+=t.textContent}(e=>{h.forEach(t=>{const n=l.a.get(f(t,e));void 0!==n&&n.keyString.forEach(e=>{const{element:{content:t}}=e,n=new Set;Array.from(t.querySelectorAll("style")).forEach(e=>{n.add(e)}),i(e,n)})})})(e);const u=o.content;n?function(e,t,n=null){const{element:{content:o},parts:r}=e;if(null==n)return void o.appendChild(t);const i=document.createTreeWalker(o,133,null,!1);let a=s(r),l=0,d=-1;for(;i.nextNode();){d++;for(i.currentNode===n&&(l=c(t),n.parentNode.insertBefore(t,n));-1!==a&&r[a].index===d;){if(l>0){for(;-1!==a;)r[a].index+=l,a=s(r,a);return}a=s(r,a)}}}(n,d,u.firstChild):u.insertBefore(d,u.firstChild),window.ShadyCSS.prepareTemplateStyles(o,e);const p=u.querySelector("style");if(window.ShadyCSS.nativeShadow&&null!==p)t.insertBefore(p.cloneNode(!0),t.firstChild);else if(n){u.insertBefore(d,u.firstChild);const e=new Set;e.add(d),i(n,e)}};window.JSCompiler_renameProperty=(e,t)=>e;const g={toAttribute(e,t){switch(t){case Boolean:return e?"":null;case Object:case Array:return null==e?e:JSON.stringify(e)}return e},fromAttribute(e,t){switch(t){case Boolean:return null!==e;case Number:return null===e?null:Number(e);case Object:case Array:return JSON.parse(e)}return e}},x=(e,t)=>t!==e&&(t==t||e==e),y={attribute:!0,type:String,converter:g,reflect:!1,hasChanged:x};class k extends HTMLElement{constructor(){super(),this.initialize()}static get observedAttributes(){this.finalize();const e=[];return this._classProperties.forEach((t,n)=>{const o=this._attributeNameForProperty(n,t);void 0!==o&&(this._attributeToPropertyMap.set(o,n),e.push(o))}),e}static _ensureClassProperties(){if(!this.hasOwnProperty(JSCompiler_renameProperty("_classProperties",this))){this._classProperties=new Map;const e=Object.getPrototypeOf(this)._classProperties;void 0!==e&&e.forEach((e,t)=>this._classProperties.set(t,e))}}static createProperty(e,t=y){if(this._ensureClassProperties(),this._classProperties.set(e,t),t.noAccessor||this.prototype.hasOwnProperty(e))return;const n="symbol"==typeof e?Symbol():"__"+e,o=this.getPropertyDescriptor(e,n,t);void 0!==o&&Object.defineProperty(this.prototype,e,o)}static getPropertyDescriptor(e,t,n){return{get(){return this[t]},set(o){const r=this[e];this[t]=o,this.requestUpdateInternal(e,r,n)},configurable:!0,enumerable:!0}}static getPropertyOptions(e){return this._classProperties&&this._classProperties.get(e)||y}static finalize(){const e=Object.getPrototypeOf(this);if(e.hasOwnProperty("finalized")||e.finalize(),this.finalized=!0,this._ensureClassProperties(),this._attributeToPropertyMap=new Map,this.hasOwnProperty(JSCompiler_renameProperty("properties",this))){const e=this.properties,t=[...Object.getOwnPropertyNames(e),..."function"==typeof Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e):[]];for(const n of t)this.createProperty(n,e[n])}}static _attributeNameForProperty(e,t){const n=t.attribute;return!1===n?void 0:"string"==typeof n?n:"string"==typeof e?e.toLowerCase():void 0}static _valueHasChanged(e,t,n=x){return n(e,t)}static _propertyValueFromAttribute(e,t){const n=t.type,o=t.converter||g,r="function"==typeof o?o:o.fromAttribute;return r?r(e,n):e}static _propertyValueToAttribute(e,t){if(void 0===t.reflect)return;const n=t.type,o=t.converter;return(o&&o.toAttribute||g.toAttribute)(e,n)}initialize(){this._updateState=0,this._updatePromise=new Promise(e=>this._enableUpdatingResolver=e),this._changedProperties=new Map,this._saveInstanceProperties(),this.requestUpdateInternal()}_saveInstanceProperties(){this.constructor._classProperties.forEach((e,t)=>{if(this.hasOwnProperty(t)){const e=this[t];delete this[t],this._instanceProperties||(this._instanceProperties=new Map),this._instanceProperties.set(t,e)}})}_applyInstanceProperties(){this._instanceProperties.forEach((e,t)=>this[t]=e),this._instanceProperties=void 0}connectedCallback(){this.enableUpdating()}enableUpdating(){void 0!==this._enableUpdatingResolver&&(this._enableUpdatingResolver(),this._enableUpdatingResolver=void 0)}disconnectedCallback(){}attributeChangedCallback(e,t,n){t!==n&&this._attributeToProperty(e,n)}_propertyToAttribute(e,t,n=y){const o=this.constructor,r=o._attributeNameForProperty(e,n);if(void 0!==r){const e=o._propertyValueToAttribute(t,n);if(void 0===e)return;this._updateState=8|this._updateState,null==e?this.removeAttribute(r):this.setAttribute(r,e),this._updateState=-9&this._updateState}}_attributeToProperty(e,t){if(8&this._updateState)return;const n=this.constructor,o=n._attributeToPropertyMap.get(e);if(void 0!==o){const e=n.getPropertyOptions(o);this._updateState=16|this._updateState,this[o]=n._propertyValueFromAttribute(t,e),this._updateState=-17&this._updateState}}requestUpdateInternal(e,t,n){let o=!0;if(void 0!==e){const r=this.constructor;n=n||r.getPropertyOptions(e),r._valueHasChanged(this[e],t,n.hasChanged)?(this._changedProperties.has(e)||this._changedProperties.set(e,t),!0!==n.reflect||16&this._updateState||(void 0===this._reflectingProperties&&(this._reflectingProperties=new Map),this._reflectingProperties.set(e,n))):o=!1}!this._hasRequestedUpdate&&o&&(this._updatePromise=this._enqueueUpdate())}requestUpdate(e,t){return this.requestUpdateInternal(e,t),this.updateComplete}async _enqueueUpdate(){this._updateState=4|this._updateState;try{await this._updatePromise}catch(e){}const e=this.performUpdate();return null!=e&&await e,!this._hasRequestedUpdate}get _hasRequestedUpdate(){return 4&this._updateState}get hasUpdated(){return 1&this._updateState}performUpdate(){if(!this._hasRequestedUpdate)return;this._instanceProperties&&this._applyInstanceProperties();let e=!1;const t=this._changedProperties;try{e=this.shouldUpdate(t),e?this.update(t):this._markUpdated()}catch(t){throw e=!1,this._markUpdated(),t}e&&(1&this._updateState||(this._updateState=1|this._updateState,this.firstUpdated(t)),this.updated(t))}_markUpdated(){this._changedProperties=new Map,this._updateState=-5&this._updateState}get updateComplete(){return this._getUpdateComplete()}_getUpdateComplete(){return this._updatePromise}shouldUpdate(e){return!0}update(e){void 0!==this._reflectingProperties&&this._reflectingProperties.size>0&&(this._reflectingProperties.forEach((e,t)=>this._propertyToAttribute(t,this[t],e)),this._reflectingProperties=void 0),this._markUpdated()}updated(e){}firstUpdated(e){}}k.finalized=!0;const _=Element.prototype;_.msMatchesSelector||_.webkitMatchesSelector;
/**
@license
Copyright (c) 2019 The Polymer Project Authors. All rights reserved.
This code may only be used under the BSD style license found at
http://polymer.github.io/LICENSE.txt The complete set of authors may be found at
http://polymer.github.io/AUTHORS.txt The complete set of contributors may be
found at http://polymer.github.io/CONTRIBUTORS.txt Code distributed by Google as
part of the polymer project is also subject to an additional IP rights grant
found at http://polymer.github.io/PATENTS.txt
*/
const w=window.ShadowRoot&&(void 0===window.ShadyCSS||window.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,S=Symbol();class O{constructor(e,t){if(t!==S)throw new Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=e}get styleSheet(){return void 0===this._styleSheet&&(w?(this._styleSheet=new CSSStyleSheet,this._styleSheet.replaceSync(this.cssText)):this._styleSheet=null),this._styleSheet}toString(){return this.cssText}}const z=(e,...t)=>{const n=t.reduce((t,n,o)=>t+(e=>{if(e instanceof O)return e.cssText;if("number"==typeof e)return e;throw new Error(`Value passed to 'css' function must be a 'css' function result: ${e}. Use 'unsafeCSS' to pass non-literal values, but\n            take care to ensure page security.`)})(n)+e[o+1],e[0]);return new O(n,S)};
/**
 * @license
 * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */
(window.litElementVersions||(window.litElementVersions=[])).push("2.4.0");const j={};class P extends k{static getStyles(){return this.styles}static _getUniqueStyles(){if(this.hasOwnProperty(JSCompiler_renameProperty("_styles",this)))return;const e=this.getStyles();if(Array.isArray(e)){const t=(e,n)=>e.reduceRight((e,n)=>Array.isArray(n)?t(n,e):(e.add(n),e),n),n=t(e,new Set),o=[];n.forEach(e=>o.unshift(e)),this._styles=o}else this._styles=void 0===e?[]:[e];this._styles=this._styles.map(e=>{if(e instanceof CSSStyleSheet&&!w){const t=Array.prototype.slice.call(e.cssRules).reduce((e,t)=>e+t.cssText,"");return new O(String(t),S)}return e})}initialize(){super.initialize(),this.constructor._getUniqueStyles(),this.renderRoot=this.createRenderRoot(),window.ShadowRoot&&this.renderRoot instanceof window.ShadowRoot&&this.adoptStyles()}createRenderRoot(){return this.attachShadow({mode:"open"})}adoptStyles(){const e=this.constructor._styles;0!==e.length&&(void 0===window.ShadyCSS||window.ShadyCSS.nativeShadow?w?this.renderRoot.adoptedStyleSheets=e.map(e=>e instanceof CSSStyleSheet?e:e.styleSheet):this._needsShimAdoptedStyleSheets=!0:window.ShadyCSS.ScopingShim.prepareAdoptedCssText(e.map(e=>e.cssText),this.localName))}connectedCallback(){super.connectedCallback(),this.hasUpdated&&void 0!==window.ShadyCSS&&window.ShadyCSS.styleElement(this)}update(e){const t=this.render();super.update(e),t!==j&&this.constructor.render(t,this.renderRoot,{scopeName:this.localName,eventContext:this}),this._needsShimAdoptedStyleSheets&&(this._needsShimAdoptedStyleSheets=!1,this.constructor._styles.forEach(e=>{const t=document.createElement("style");t.textContent=e.cssText,this.renderRoot.appendChild(t)}))}render(){return j}}P.finalized=!0,P.render=(e,t,n)=>{if(!n||"object"!=typeof n||!n.scopeName)throw new Error("The `scopeName` option is required.");const r=n.scopeName,i=a.a.has(t),c=p&&11===t.nodeType&&!!t.host,s=c&&!m.has(r),l=s?document.createDocumentFragment():t;if(Object(a.b)(e,l,Object.assign({templateFactory:b(r)},n)),s){const e=a.a.get(l);a.a.delete(l);const n=e.value instanceof d.a?e.value.template:void 0;v(r,l,n),Object(o.b)(t,t.firstChild),t.appendChild(l),a.a.set(t,e)}!i&&c&&window.ShadyCSS.styleElement(t.host)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return r})),n.d(t,"b",(function(){return i}));
/**
 * @license
 * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */
const o="undefined"!=typeof window&&null!=window.customElements&&void 0!==window.customElements.polyfillWrapFlushCallback,r=(e,t,n=null,o=null)=>{for(;t!==n;){const n=t.nextSibling;e.insertBefore(t,o),t=n}},i=(e,t,n=null)=>{for(;t!==n;){const n=t.nextSibling;e.removeChild(t),t=n}}},function(e,t,n){"use strict";n.d(t,"b",(function(){return c.a})),n.d(t,"a",(function(){return o.b})),n.d(t,"c",(function(){return s}));var o=n(1);
/**
 * @license
 * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */const r=new class{handleAttributeExpressions(e,t,n,r){const i=t[0];if("."===i){return new o.f(e,t.slice(1),n).parts}if("@"===i)return[new o.d(e,t.slice(1),r.eventContext)];if("?"===i)return[new o.c(e,t.slice(1),n)];return new o.a(e,t,n).parts}handleTextExpression(e){return new o.e(e)}};var i=n(9),c=n(8);n(4),n(2),n(7),n(6),n(10),n(0);
/**
 * @license
 * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */
"undefined"!=typeof window&&(window.litHtmlVersions||(window.litHtmlVersions=[])).push("1.3.0");const s=(e,...t)=>new i.b(e,t,"html",r)},function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var o=n(0);
/**
 * @license
 * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */function r(e){let t=i.get(e.type);void 0===t&&(t={stringsArray:new WeakMap,keyString:new Map},i.set(e.type,t));let n=t.stringsArray.get(e.strings);if(void 0!==n)return n;const r=e.strings.join(o.f);return n=t.keyString.get(r),void 0===n&&(n=new o.a(e,e.getTemplateElement()),t.keyString.set(r,n)),t.stringsArray.set(e.strings,n),n}const i=new Map},function(e,t,n){"use strict";n.d(t,"a",(function(){return c})),n.d(t,"b",(function(){return s}));var o=n(4),r=n(1),i=n(6);
/**
 * @license
 * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */
const c=new WeakMap,s=(e,t,n)=>{let s=c.get(t);void 0===s&&(Object(o.b)(t,t.firstChild),c.set(t,s=new r.e(Object.assign({templateFactory:i.b},n))),s.appendInto(t)),s.setValue(e),s.commit()}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));
/**
 * @license
 * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */
const o=new WeakMap,r=e=>(...t)=>{const n=e(...t);return o.set(n,!0),n},i=e=>"function"==typeof e&&o.has(e)},function(e,t,n){"use strict";n.d(t,"b",(function(){return s})),n.d(t,"a",(function(){return a}));var o=n(4),r=n(0);
/**
 * @license
 * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */
const i=window.trustedTypes&&trustedTypes.createPolicy("lit-html",{createHTML:e=>e}),c=` ${r.f} `;class s{constructor(e,t,n,o){this.strings=e,this.values=t,this.type=n,this.processor=o}getHTML(){const e=this.strings.length-1;let t="",n=!1;for(let o=0;o<e;o++){const e=this.strings[o],i=e.lastIndexOf("\x3c!--");n=(i>-1||n)&&-1===e.indexOf("--\x3e",i+1);const s=r.e.exec(e);t+=null===s?e+(n?c:r.g):e.substr(0,s.index)+s[1]+s[2]+r.b+s[3]+r.f}return t+=this.strings[e],t}getTemplateElement(){const e=document.createElement("template");let t=this.getHTML();return void 0!==i&&(t=i.createHTML(t)),e.innerHTML=t,e}}class a extends s{getHTML(){return`<svg>${super.getHTML()}</svg>`}getTemplateElement(){const e=super.getTemplateElement(),t=e.content,n=t.firstChild;return t.removeChild(n),Object(o.c)(t,n.firstChild),e}}},function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var o=n(4),r=n(0);
/**
 * @license
 * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */
class i{constructor(e,t,n){this.__parts=[],this.template=e,this.processor=t,this.options=n}update(e){let t=0;for(const n of this.__parts)void 0!==n&&n.setValue(e[t]),t++;for(const e of this.__parts)void 0!==e&&e.commit()}_clone(){const e=o.a?this.template.element.content.cloneNode(!0):document.importNode(this.template.element.content,!0),t=[],n=this.template.parts,i=document.createTreeWalker(e,133,null,!1);let c,s=0,a=0,l=i.nextNode();for(;s<n.length;)if(c=n[s],Object(r.d)(c)){for(;a<c.index;)a++,"TEMPLATE"===l.nodeName&&(t.push(l),i.currentNode=l.content),null===(l=i.nextNode())&&(i.currentNode=t.pop(),l=i.nextNode());if("node"===c.type){const e=this.processor.handleTextExpression(this.options);e.insertAfterNode(l.previousSibling),this.__parts.push(e)}else this.__parts.push(...this.processor.handleAttributeExpressions(l,c.name,c.strings,this.options));s++}else this.__parts.push(void 0),s++;return o.a&&(document.adoptNode(e),customElements.upgrade(e)),e}}},function(e,t,n){var o;
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var n={}.hasOwnProperty;function r(){for(var e=[],t=0;t<arguments.length;t++){var o=arguments[t];if(o){var i=typeof o;if("string"===i||"number"===i)e.push(o);else if(Array.isArray(o)&&o.length){var c=r.apply(null,o);c&&e.push(c)}else if("object"===i)for(var s in o)n.call(o,s)&&o[s]&&e.push(s)}}return e.join(" ")}e.exports?(r.default=r,e.exports=r):void 0===(o=function(){return r}.apply(t,[]))||(e.exports=o)}()},function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var o=n(5);
/**
 * @license
 * Copyright (c) 2018 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */const r=new WeakMap,i=Object(o.b)(e=>t=>{const n=r.get(t);if(void 0===e&&t instanceof o.a){if(void 0!==n||!r.has(t)){const e=t.committer.name;t.committer.element.removeAttribute(e)}}else e!==n&&t.setValue(e);r.set(t,e)})},function(e,t,n){"use strict";n.r(t);var o=n(3),r=n(11),i=n.n(r),c=o.b`.cvds-icon:before{font-style:normal;font-weight:400;font-variant:normal;vertical-align:middle;speak:none;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.cvds-icon--size-s:before{font-size:1.25rem}.cvds-icon--size-m:before{font-size:1.875rem}.cvds-icon--size-l:before{font-size:2.5rem}.cvds-icon--size-xl:before{font-size:3.4375rem}.cvds-icon--style-flat:before{font-family:"CV UNI Icons flat"}.cvds-icon--style-outline:before{font-family:"CV UNI Icons outline"}.cvds-icon--Audio:before{content:""}.cvds-icon--Audio_auf_CD:before{content:""}.cvds-icon--Audio_auf_USB:before{content:""}.cvds-icon--Audio_online:before{content:""}.cvds-icon--Video:before{content:""}.cvds-icon--Video_auf_CD:before{content:""}.cvds-icon--Video_auf_USB:before{content:""}.cvds-icon--Video_online:before{content:""}.cvds-icon--Visualisiertes_Audio:before{content:""}.cvds-icon--Interaktive_uebungen:before{content:""}.cvds-icon--eBook:before{content:""}.cvds-icon--eBook_plus:before{content:""}.cvds-icon--App:before{content:""}.cvds-icon--Unterrichtsmanager:before{content:""}.cvds-icon--Vokabeltrainer-App:before{content:""}.cvds-icon--Link_auf_Webseite:before{content:""}.cvds-icon--Mixed_Media:before{content:""}.cvds-icon--Bild:before{content:""}.cvds-icon--Whiteboard:before{content:""}.cvds-icon--Computer:before{content:""}.cvds-icon--Tablet:before{content:""}.cvds-icon--Smartphone:before{content:""}.cvds-icon--USB-Stick:before{content:""}.cvds-icon--CD_DVD:before{content:""}.cvds-icon--Cloud:before{content:""}.cvds-icon--Dokument:before{content:""}.cvds-icon--Word-Dokument:before{content:""}.cvds-icon--Excel-Dokument:before{content:""}.cvds-icon--PDF-Dokument:before{content:""}.cvds-icon--PowerPoint-Dokument:before{content:""}.cvds-icon--ZIP-Datei:before{content:""}.cvds-icon--Schuelerbuch:before{content:""}.cvds-icon--Arbeitsheft:before{content:""}.cvds-icon--Lektuere:before{content:""}.cvds-icon--Handreichung:before{content:""}.cvds-icon--Kopiervorlage:before{content:""}.cvds-icon--Online-Angebot:before{content:""}.cvds-icon--Partnerarbeit:before{content:""}.cvds-icon--Gruppenarbeit:before{content:""}.cvds-icon--Kooperative_Lernform:before{content:""}.cvds-icon--Szenisches_Spiel:before{content:""}.cvds-icon--Klassengespraech:before{content:""}.cvds-icon--Hoerverstehen:before{content:""}.cvds-icon--Hoer-Sehverstehen:before{content:""}.cvds-icon--Lesen:before{content:""}.cvds-icon--Einzelarbeit:before{content:""}.cvds-icon--Lesen_mit_Partner:before{content:""}.cvds-icon--Schreiben:before{content:""}.cvds-icon--Schreiben_ins_Heft:before{content:""}.cvds-icon--Sprechen:before{content:""}.cvds-icon--Phonetik:before{content:""}.cvds-icon--Sprachmittlung_EN-DE:before{content:""}.cvds-icon--Sprachmittlung_IT-DE:before{content:""}.cvds-icon--Sprachmittlung_RU-DE:before{content:""}.cvds-icon--Dialog:before{content:""}.cvds-icon--Sehen_oder_Einblenden:before{content:""}.cvds-icon--Beobachten:before{content:""}.cvds-icon--Handeln:before{content:""}.cvds-icon--Recherchieren:before{content:""}.cvds-icon--Nachdenken:before{content:""}.cvds-icon--Markierung:before{content:""}.cvds-icon--Unterstreichen:before{content:""}.cvds-icon--Einkreisen:before{content:""}.cvds-icon--Ankreuzen:before{content:""}.cvds-icon--Abhaken:before{content:""}.cvds-icon--Nummerieren:before{content:""}.cvds-icon--Zuordnen:before{content:""}.cvds-icon--Verbinden:before{content:""}.cvds-icon--Nachspuren:before{content:""}.cvds-icon--Buchstabenkarten:before{content:""}.cvds-icon--Ausschneiden:before{content:""}.cvds-icon--Kleben:before{content:""}.cvds-icon--Malen:before{content:""}.cvds-icon--Spielen:before{content:""}.cvds-icon--Musik:before{content:""}.cvds-icon--Landeskunde:before{content:""}.cvds-icon--Lern-_und_Arbeitstechniken:before{content:""}.cvds-icon--Medienkompetenz:before{content:""}.cvds-icon--Partner-Check:before{content:""}.cvds-icon--Einzelarbeit_GS:before{content:""}.cvds-icon--Partnerarbeit_GS:before{content:""}.cvds-icon--Gruppenarbeit_GS:before{content:""}.cvds-icon--Lesen_mit_Partner_GS:before{content:""}.cvds-icon--Partner-Check_GS:before{content:""}.cvds-icon--Tabelle_anfertigen:before{content:""}.cvds-icon--Stichworte_notieren:before{content:""}.cvds-icon--In_Schritten_loesen:before{content:""}.cvds-icon--Silbenboegen:before{content:""}.cvds-icon--Bleistift_und_Lineal_verwenden:before{content:""}.cvds-icon--Spielstein:before{content:""}.cvds-icon--Instrument_spielen:before{content:""}.cvds-icon--Singen:before{content:""}.cvds-icon--Tanzen:before{content:""}.cvds-icon--Musikanalyse:before{content:""}.cvds-icon--Nachschlagen:before{content:""}.cvds-icon--Durchstreichen:before{content:""}.cvds-icon--Abschreiben:before{content:""}.cvds-icon--Schwierigkeit_1_von_4b:before{content:""}.cvds-icon--sehr_leichte_Aufgabe:before{content:""}.cvds-icon--Differenzierung_1:before{content:""}.cvds-icon--leichte_Aufgabe:before{content:""}.cvds-icon--Differenzierung_2:before{content:""}.cvds-icon--Schwierigkeit_1_von_3:before{content:""}.cvds-icon--mittlere_Aufgabe:before{content:""}.cvds-icon--Differenzierung_3:before{content:""}.cvds-icon--Schwierigkeit_2_von_3:before{content:""}.cvds-icon--schwere_Aufgabe:before{content:""}.cvds-icon--Differenzierung_4:before{content:""}.cvds-icon--Schwierigkeit_3_von_3:before{content:""}.cvds-icon--Grundkurs:before{content:""}.cvds-icon--Erweiterungskurs:before{content:""}.cvds-icon--erhoehte_Anforderung:before{content:""}.cvds-icon--Erweiterungsaufgabe:before{content:""}.cvds-icon--Vertiefungsaufgabe:before{content:""}.cvds-icon--Wahlaufgabe:before{content:""}.cvds-icon--Zusatzaufgabe:before{content:""}.cvds-icon--Experiment:before{content:""}.cvds-icon--Lehrerexperiment:before{content:""}.cvds-icon--Lerntagebuch:before{content:""}.cvds-icon--Portfolio:before{content:""}.cvds-icon--Tipp:before{content:""}.cvds-icon--Laufdiktat:before{content:""}.cvds-icon--Klappdiktat:before{content:""}.cvds-icon--Fragediktat:before{content:""}.cvds-icon--Dosendiktat:before{content:""}.cvds-icon--Wuerfeldiktat:before{content:""}.cvds-icon--Partnerdiktat:before{content:""}.cvds-icon--Partnerdiktat_GS:before{content:""}.cvds-icon--Verweis_Pfeil:before{content:""}.cvds-icon--Verweis_Dreieck:before{content:""}.cvds-icon--Literaturhinweis:before{content:""}.cvds-icon--Schwingen:before{content:""}.cvds-icon--Verlaengern:before{content:""}.cvds-icon--Ableiten:before{content:""}.cvds-icon--Merken:before{content:""}.cvds-icon--Gross-_und_Kleinschreibung:before{content:""}.cvds-icon--Rechtschreibstrategie:before{content:""}.cvds-icon--Getrennt-_und_Zusammenschreibung:before{content:""}.cvds-icon--Im_Woerterbuch_suchen_oder_Hilfe:before{content:""}.cvds-icon--Zerlegen:before{content:""}.cvds-icon--Zeichensetzung:before{content:""}.cvds-icon--Stolperstelle_Schild:before{content:""}.cvds-icon--Selbstbewertung_1:before{content:""}.cvds-icon--Selbstbewertung_2:before{content:""}.cvds-icon--Selbstbewertung_3:before{content:""}.cvds-icon--Geschlossene_Aufgabe_oder_Locked:before{content:""}.cvds-icon--Offene_Aufgaben_oder_Unlocked:before{content:""}.cvds-icon--Information:before{content:""}.cvds-icon--Merken_Wichtig:before{content:""}.cvds-icon--Taschenrechner:before{content:""}.cvds-icon--keine_technischen_Hilfsmittel:before{content:""}.cvds-icon--TK:before{content:""}.cvds-icon--DGS:before{content:""}.cvds-icon--GTR:before{content:""}.cvds-icon--CAS:before{content:""}.cvds-icon--WTR:before{content:""}.cvds-icon--TR:before{content:""}.cvds-icon--Aufgabenerweiterung:before{content:""}.cvds-icon--Lehrergespraech:before{content:""}.cvds-icon--Download:before{content:""}.cvds-icon--Cloud-Download:before{content:""}.cvds-icon--Dokument_download:before{content:""}.cvds-icon--Loeschen:before{content:""}.cvds-icon--Speichern:before{content:""}.cvds-icon--Versenden:before{content:""}.cvds-icon--Pointer:before{content:""}.cvds-icon--Einstellungen:before{content:""}.cvds-icon--Reset:before{content:""}.cvds-icon--Reload:before{content:""}.cvds-icon--Wechsel:before{content:""}.cvds-icon--Dragable:before{content:""}.cvds-icon--Tools:before{content:""}.cvds-icon--Tablet_Landscape:before{content:""}.cvds-icon--Smartphone_Landscape:before{content:""}.cvds-icon--Augmented_Print:before{content:""}.cvds-icon--Lesehilfe:before{content:""}.cvds-icon--Verdecken:before{content:""}.cvds-icon--Highlight:before{content:""}.cvds-icon--Bookmark:before{content:""}.cvds-icon--Wichtig:before{content:""}.cvds-icon--Neue_Notiz:before{content:""}.cvds-icon--Hinzufuegen:before{content:""}.cvds-icon--Suchen:before{content:""}.cvds-icon--Check:before{content:""}.cvds-icon--Korrekt:before{content:""}.cvds-icon--Abbrechen:before{content:""}.cvds-icon--Schliessen:before{content:""}.cvds-icon--Ausblenden:before{content:""}.cvds-icon--Schueler-Ansicht:before{content:""}.cvds-icon--Lehrer-Ansicht:before{content:""}.cvds-icon--Kalender:before{content:""}.cvds-icon--Stundenplaner:before{content:""}.cvds-icon--Statistik:before{content:""}.cvds-icon--Warenkorb:before{content:""}.cvds-icon--Anbeamen:before{content:""}.cvds-icon--Ausdrucken:before{content:""}.cvds-icon--Rating_none:before{content:""}.cvds-icon--Rating_half:before{content:""}.cvds-icon--Rating_full:before{content:""}.cvds-icon--Abmelden:before{content:""}.cvds-icon--Sortieren:before{content:""}.cvds-icon--Sortieren_aufsteigend:before{content:""}.cvds-icon--Sortieren_absteigend:before{content:""}.cvds-icon--Liste:before{content:""}.cvds-icon--Mehr-Menu:before{content:""}.cvds-icon--Link:before{content:""}.cvds-icon--externer_Link:before{content:""}.cvds-icon--Show_Grid:before{content:""}.cvds-icon--Show_Single:before{content:""}.cvds-icon--Vergroessern:before{content:""}.cvds-icon--Verkleinern:before{content:""}.cvds-icon--Auswahl_zoomen:before{content:""}.cvds-icon--Default-Zustand:before{content:""}.cvds-icon--Vollbildmodus:before{content:""}.cvds-icon--Vollbildmodus_beenden:before{content:""}.cvds-icon--Markieren:before{content:""}.cvds-icon--Stolperstelle_Warnung:before{content:""}.cvds-icon--Loesungsschluessel:before{content:""}.cvds-icon--Uhr:before{content:""}.cvds-icon--Benachrichtigungen:before{content:""}.cvds-icon--Erfolg_niedrig:before{content:""}.cvds-icon--Erfolg_mittel:before{content:""}.cvds-icon--Erfolg_hoch:before{content:""}.cvds-icon--Editieren:before{content:""}.cvds-icon--Textwerkzeug:before{content:""}.cvds-icon--Bold:before{content:""}.cvds-icon--Underline:before{content:""}.cvds-icon--Italic:before{content:""}.cvds-icon--Radiergummi:before{content:""}.cvds-icon--Brief:before{content:""}.cvds-icon--Telefon:before{content:""}.cvds-icon--Nutzerprofil:before{content:""}.cvds-icon--Share:before{content:""}.cvds-icon--Broadcast:before{content:""}.cvds-icon--Foto:before{content:""}.cvds-icon--Play:before{content:""}.cvds-icon--Pause:before{content:""}.cvds-icon--Stop:before{content:""}.cvds-icon--Ton_laut:before{content:""}.cvds-icon--Ton_leise:before{content:""}.cvds-icon--Ton_aus:before{content:""}.cvds-icon--Headphones:before{content:""}.cvds-icon--Sprachaufnahme_aktivieren:before{content:""}.cvds-icon--Mikrofon_deaktivieren:before{content:""}.cvds-icon--Untertitel:before{content:""}.cvds-icon--Menu:before{content:""}.cvds-icon--Menu_links:before{content:""}.cvds-icon--Menu_rechts:before{content:""}.cvds-icon--Zurueck:before{content:""}.cvds-icon--Pfeil_nach_links:before{content:""}.cvds-icon--Pfeil_nach_rechts:before{content:""}.cvds-icon--Pfeil_nach_oben:before{content:""}.cvds-icon--Pfeil_nach_unten:before{content:""}.cvds-icon--Chevron_links:before{content:""}.cvds-icon--Chevron_rechts:before{content:""}.cvds-icon--Weiter:before{content:""}.cvds-icon--Chevron_oben:before{content:""}.cvds-icon--Chevron_unten:before{content:""}.cvds-icon--First_page:before{content:""}.cvds-icon--Last_page:before{content:""}.cvds-icon--Meine_Klasse:before{content:""}.cvds-icon--Home:before{content:""}.cvds-icon--Bibliothek:before{content:""}.cvds-icon--Tastatur:before{content:""}.cvds-icon--Weniger:before{content:""}.cvds-icon--Blick_ins_Buch:before{content:""}.cvds-icon--Online_Magazin:before{content:""}.cvds-icon--Merkzettel:before{content:""}.cvds-icon--Login:before{content:""}.cvds-icon--Satzzaehlung:before{content:""}.cvds-icon--Training:before{content:""}.cvds-icon--Meisterschaft:before{content:""}.cvds-icon--Erfolg_leer:before{content:""}.cvds-icon--Textkorrektur:before{content:""}.cvds-icon--Pfeil_rechts_gross:before{content:""}.cvds-icon--Dropdown_close:before{content:""}.cvds-icon--Dropdown_open:before{content:""}.cvds-icon--Produktkranz:before{content:""}.cvds-icon--Autoren_Herausgeber:before{content:""}.cvds-icon--Bildergalerie:before{content:""}.cvds-icon--Doppelseite:before{content:""}.cvds-icon--Einzelseite:before{content:""}.cvds-icon--WLAN:before{content:""}.cvds-icon--WLAN_off:before{content:""}.cvds-icon--Kopieren:before{content:""}.cvds-icon--Tastatur_einblenden:before{content:""}.cvds-icon--Tastatur_ausblenden:before{content:""}.cvds-icon--Hilfe_einblenden:before{content:""}.cvds-icon--Hilfe_ausblenden:before{content:""}.cvds-icon--Handschrifteingabe:before{content:""}.cvds-icon--Buchreihe:before{content:""}.cvds-icon--Checkmark:before{content:""}.cvds-icon--Nicht_bearbeitbar:before{content:""}.cvds-icon--QR-Code:before{content:""}.cvds-icon--editierbare_Kopiervorlage:before{content:""}.cvds-icon--Arbeitsheft_thematisch:before{content:""}.cvds-icon--Themenband:before{content:""}.cvds-icon--Diagnose_und_Foerdern:before{content:""}.cvds-icon--Filter:before{content:""}.cvds-icon--BigBook:before{content:""}.cvds-icon--Museumsrundgang:before{content:""}.cvds-icon--Reflexionsphase:before{content:""}.cvds-icon--Medienkompetenz_Grundschule:before{content:""}.cvds-icon--Paket:before{content:""}.cvds-icon--Ordner:before{content:""}.cvds-icon--Ordner_erstellen:before{content:""}.cvds-icon--Ordner_verschieben:before{content:""}.cvds-icon--Ordner_hochladen:before{content:""}.cvds-icon--Ordner_runterladen:before{content:""}.cvds-icon--Ordner_offen:before{content:""}.cvds-icon--Instagram:before{content:""}.cvds-icon--Twitter:before{content:""}.cvds-icon--Facebook:before{content:""}.cvds-icon--Google_Plus:before{content:""}.cvds-icon--Youtube:before{content:""}.cvds-icon--LinkedIn:before{content:""}.cvds-icon--Xing:before{content:""}.cvds-icon--WhatsApp:before{content:""}.cvds-icon--Puzzle:before{content:""}.cvds-icon--Anhang:before{content:""}.cvds-icon--Zahnraeder:before{content:""}.cvds-icon--Dokument_digital:before{content:""}.cvds-icon--Dokument_Ablauf_50:before{content:""}.cvds-icon--Dokument_Ablauf_100:before{content:""}.cvds-icon--Programmierer:before{content:""}.cvds-icon--Redakteur:before{content:""}.cvds-icon--Schwierigkeit_1_von_4a:before{content:""}.cvds-icon--Schwierigkeit_2_von_4:before{content:""}.cvds-icon--Schwierigkeit_3_von_4:before{content:""}.cvds-icon--Schwierigkeit_4_von_4:before{content:""}:host{display:inline}:host([hidden]){display:none}.cvds-icon{display:inherit}.cvds-icon--size-s[data-em-font]:before,[data-em-font] .cvds-icon--size-s:before{font-size:1.25em}.cvds-icon--size-m[data-em-font]:before,[data-em-font] .cvds-icon--size-m:before{font-size:1.875em}.cvds-icon--size-l[data-em-font]:before,[data-em-font] .cvds-icon--size-l:before{font-size:2.5em}.cvds-icon--size-xl[data-em-font]:before,[data-em-font] .cvds-icon--size-xl:before{font-size:3.4375em}`;function s(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['<span class="','" ?data-em-font="','"></span>']);return s=function(){return e},e}class a extends o.a{static get styles(){return[c]}static get properties(){return{icon:{type:String},size:{type:String},style:{type:String},emFont:{type:Boolean}}}constructor(){super(),this.style="outline"}render(){var e=i()("cvds-icon",{["".concat("cvds-icon","--").concat(this.icon)]:this.icon,["".concat("cvds-icon","--size-").concat(this.size)]:this.size,["".concat("cvds-icon","--style-").concat(this.style)]:this.style});return Object(o.c)(s(),e,this.emFont)}}customElements.define("dkp-icon",a)},function(e,t,n){"use strict";n.r(t),n.d(t,"formatDate",(function(){return o})),n.d(t,"isToday",(function(){return r})),n.d(t,"remainingTimeInDays",(function(){return i}));var o=e=>new Intl.DateTimeFormat("de-DE").format(e),r=e=>{var t=new Date;return e.getDate()==t.getDate()&&e.getMonth()==t.getMonth()&&e.getFullYear()==t.getFullYear()},i=e=>{var t=new Date,n=e.getTime()-t.getTime();return Math.ceil(n/864e5)}},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`:host{display:inline-block;font-size:0;--badge-background-color:#B73E06;--badge-color:#FFFFFF}:host([hidden]){display:none}.badge{display:inherit;padding:2px 7px;border-radius:2px;background-color:var(--badge-background-color);color:var(--badge-color);text-transform:uppercase;font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.8125rem;font-weight:400;line-height:1.0625rem}`;function i(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['<span class="','">',"</span>\n    "]);return i=function(){return e},e}class c extends o.a{static get styles(){return[r]}static get properties(){return{label:{type:String}}}render(){return Object(o.c)(i(),"badge",this.label)}}customElements.define("dkp-badge",c)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=n(11),i=n.n(r),c=(n(13),o.b`:host{display:inline-block;width:100%;--link-button--color:#FFFFFF;--link-button--background-color:#007688;--link-button--border-color:#007688;--link-button--focus-color:#007688;--link-button--hover-color:#FFFFFF;--link-button--hover-background-color:#005867;--link-button--hover-border-color:#005867;--link-button--disabled-color:#FFFFFF;--link-button--disabled-background-color:#757575;--link-button--disabled-border-color:#757575;--link-button--disabled-hover-color:#FFFFFF;--link-button--disabled-hover-background-color:#757575;--link-button--disabled-hover-border-color:#757575;--link-button--secondary-color:#595959;--link-button--secondary-background-color:#FFFFFF;--link-button--secondary-border-color:#BBBBBB;--link-button--secondary-hover-color:#007688;--link-button--secondary-hover-background-color:inherit;--link-button--secondary-hover-border-color:#007688;--link-button--secondary-disabled-color:#757575;--link-button--secondary-disabled-background-color:inherit;--link-button--secondary-disabled-border-color:#E0E0E0;--link-button--secondary-disabled-hover-color:#757575;--link-button--secondary-disabled-hover-background-color:inherit;--link-button--secondary-disabled-hover-border-color:#E0E0E0}@media screen and (min-width:992px){:host{width:auto}}:host([hidden]){display:none}.link-button{transition-property:background-color,color,border,-webkit-text-decoration;transition-property:background-color,color,border,text-decoration;transition-property:background-color,color,border,text-decoration,-webkit-text-decoration;transition-duration:.4s;transition-timing-function:ease;font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.0625rem;font-weight:600;line-height:3rem;display:inline-block;position:relative;border-radius:2px;cursor:pointer;vertical-align:middle;box-sizing:border-box;padding:0 20px;white-space:nowrap;background-color:#007688;border:1px solid #007688;color:#fff;text-align:center;display:inherit;width:100%;color:var(--link-button--color);background-color:var(--link-button--background-color);border-color:var(--link-button--border-color)}.link-button,.link-button:active,.link-button:hover{text-decoration:none}.link-button:focus{outline:0}.link-button:not(:disabled):focus-visible{outline:0}.link-button:not(:disabled):focus-visible::before{content:"";outline:3px dotted #007688;outline-offset:2px;position:absolute;top:0;left:0;width:100%;height:100%}.link-button:active,.link-button:focus,.link-button:hover{text-decoration:none;background-color:#005867;border-color:#005867;color:#fff}.link-button:disabled,.link-button[disabled]{background-color:#757575;border-color:#757575}.link-button:disabled:active,.link-button:disabled:focus,.link-button:disabled:focus-visible,.link-button:disabled:hover,.link-button[disabled]:active,.link-button[disabled]:focus,.link-button[disabled]:focus-visible,.link-button[disabled]:hover{outline:0;text-decoration:none;background-color:#757575;border-color:#757575}@media screen and (min-width:992px){.link-button{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.9375rem;font-weight:600;line-height:2.6875rem}}.link-button:after,.link-button:before{content:"";display:table}.link-button:after{clear:both}@media screen and (min-width:768px){.link-button{max-width:360px}}.link-button:active,.link-button:focus,.link-button:hover{color:var(--link-button--hover-color);background-color:var(--link-button--hover-background-color);border-color:var(--link-button--hover-border-color)}.link-button[data-em-font],[data-em-font] .link-button{font-size:1.0625em;line-height:2.8235294118em}@media screen and (min-width:992px){.link-button[data-em-font],[data-em-font] .link-button{font-size:.9375em;line-height:2.8666666667em}}.link-button--s{line-height:1.875rem}.link-button--s[data-em-font],[data-em-font] .link-button--s{line-height:1.7647058824em}@media screen and (min-width:992px){.link-button--s[data-em-font],[data-em-font] .link-button--s{line-height:2em}}.link-button--l{line-height:3rem}.link-button--l[data-em-font],[data-em-font] .link-button--l{line-height:2.8235294118em}@media screen and (min-width:992px){.link-button--l[data-em-font],[data-em-font] .link-button--l{line-height:3.2em}}.link-button--disabled{background-color:var(--link-button--disabled-background-color);border-color:var(--link-button--disabled-border-color);color:var(--link-button--disabled-color)}.link-button--disabled:active,.link-button--disabled:focus,.link-button--disabled:hover{background-color:var(--link-button--disabled-hover-background-color);border-color:var(--link-button--disabled-hover-border-color);color:var(--link-button--disabled-hover-color);cursor:default}.link-button--secondary{background-color:#fff;border-color:#bbb;color:#595959;color:var(--link-button--secondary-color);background-color:var(--link-button--secondary-background-color);border-color:var(--link-button--secondary-border-color)}.link-button--secondary:disabled,.link-button--secondary[disabled]{background-color:#fff;border-color:#e0e0e0;color:#757575}.link-button--secondary:disabled:active,.link-button--secondary:disabled:focus,.link-button--secondary:disabled:hover,.link-button--secondary[disabled]:active,.link-button--secondary[disabled]:focus,.link-button--secondary[disabled]:hover{text-decoration:none;border-color:#e0e0e0;color:#757575;background-color:#fff}.link-button--secondary:active,.link-button--secondary:focus,.link-button--secondary:hover{background-color:#fff;border-color:#007688;color:#007688}.link-button--secondary:active,.link-button--secondary:focus,.link-button--secondary:hover{color:var(--link-button--secondary-hover-color);background-color:var(--link-button--secondary-hover-background-color);border-color:var(--link-button--secondary-hover-border-color)}.link-button--secondary.link-button--disabled{background-color:var(--link-button--secondary-disabled-background-color);border-color:var(--link-button--secondary-disabled-border-color);color:var(--link-button--secondary-disabled-color)}.link-button--secondary.link-button--disabled:active,.link-button--secondary.link-button--disabled:focus,.link-button--secondary.link-button--disabled:hover{border-color:var(--link-button--secondary-disabled-hover-border-color);color:var(--link-button--secondary-disabled-hover-color);background-color:var(--link-button--secondary-disabled-hover-background-colo)}dkp-icon{margin-right:10px}`);function s(){var e=f(['<dkp-icon icon="','" size="m"></dkp-icon>']);return s=function(){return e},e}function a(){var e=f(['\n        <a class="','" href="','" target="','" ?data-em-font="','">\n            ','<span class="text">',"</span>\n        </a>\n    "]);return a=function(){return e},e}function l(){var e=f(['\n        <span class="','" ?data-em-font="','">\n            ',"\n        </span>\n      "]);return l=function(){return e},e}function d(){var e=f(['<dkp-icon icon="','" size="m"></dkp-icon>']);return d=function(){return e},e}function u(){var e=f(['\n        <button class="','" ?disabled="','" ?data-em-font="','">\n          ','<span class="text">',"</span>\n        </button>\n      "]);return u=function(){return e},e}function f(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}class p extends o.a{static get styles(){return[c]}static get properties(){return{disabled:{type:Boolean},"disable-links":{type:Boolean},download:{type:Boolean},href:{type:String},label:{type:String},icon:{type:String},button:{type:Boolean},size:{type:String},variant:{type:String},"external-link":{type:Boolean},emFont:{type:Boolean}}}constructor(){super(),this.variant="primary"}render(){var e=this.icon||(this.download?"download":""),t=this.disabled||this["disable-links"],n=i()("link-button",{["".concat("link-button","--s")]:"small"===this.size,["".concat("link-button","--l")]:"large"===this.size,["".concat("link-button","--secondary")]:"secondary"===this.variant,["".concat("link-button","--disabled")]:this.disabled});return this.button?Object(o.c)(u(),n,t,this.emFont,e?Object(o.c)(d(),e):"",this.label):t?Object(o.c)(l(),n,this.emFont,this.label):Object(o.c)(a(),n,this.href,this["external-link"]?"_blank":"",this.emFont,e?Object(o.c)(s(),e):"",this.label)}}customElements.define("dkp-link-button",p)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`:host{display:block;width:auto}:host([hidden]){display:none}img{display:inherit;max-width:100%;font-style:italic;vertical-align:middle}`;function i(){var e=s(['\n      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1 1" preserveAspectRatio="none">\n          <rect width="100%" height="100%" fill="#EAEAEA"}></rect>\n          <g stroke-width="2px" stroke="#E0E0E0" vector-effect="non-scaling-stroke">\n              <line x1="0" y1="0" x2="1" y2="1" vector-effect="non-scaling-stroke"/>\n              <line x1="1" y1="0" x2="0" y2="1" vector-effect="non-scaling-stroke"/>\n          </g>\n      </svg>\n    ']);return i=function(){return e},e}function c(){var e=s(['<img src="','" alt="','" />']);return c=function(){return e},e}function s(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}class a extends o.a{static get styles(){return[r]}static get properties(){return{"image-uri":{type:String},"alt-text":{type:String}}}render(){return Boolean(this["image-uri"])||Boolean(this["alt-text"])?Object(o.c)(c(),this["image-uri"],this["alt-text"]):Object(o.c)(i())}}customElements.define("dkp-image",a)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`:host{display:block}:host([hidden]){display:none}svg{display:inherit;max-width:100%;font-style:italic;vertical-align:middle;height:auto}`;function i(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n      <svg xmlns="http://www.w3.org/2000/svg" width="280" height="403" fill="none" viewBox="0 0 280 403">\n        <path fill="#F5F5F5" d="M0 .853h280v401.692H0z"/>\n        <path fill="#9E9E9E" d="M99.879 155.353v-24.928h3.154v12.502h.114l10.374-12.502h3.572l-7.79 9.5 9.006 15.428h-3.534l-7.448-12.958-4.294 5.054v7.904h-3.154Zm28.928.456a9.435 9.435 0 0 1-3.496-.646 8.908 8.908 0 0 1-2.812-1.9c-.785-.836-1.406-1.85-1.862-3.04-.456-1.191-.684-2.546-.684-4.066s.228-2.876.684-4.066c.481-1.216 1.102-2.242 1.862-3.078a8.026 8.026 0 0 1 2.66-1.9 7.265 7.265 0 0 1 3.078-.684c1.165 0 2.204.202 3.116.608.937.405 1.71.988 2.318 1.748.633.76 1.115 1.672 1.444 2.736.329 1.064.494 2.254.494 3.572 0 .329-.013.658-.038.988 0 .304-.025.57-.076.798h-12.464c.127 1.976.735 3.546 1.824 4.712 1.115 1.14 2.559 1.71 4.332 1.71.887 0 1.697-.127 2.432-.38.76-.279 1.482-.634 2.166-1.064l1.102 2.052c-.811.506-1.71.95-2.698 1.33-.988.38-2.115.57-3.382.57Zm-5.814-11.172h9.88c0-1.875-.405-3.294-1.216-4.256-.785-.988-1.9-1.482-3.344-1.482a4.88 4.88 0 0 0-1.9.38 4.868 4.868 0 0 0-1.596 1.14c-.481.481-.887 1.076-1.216 1.786-.304.709-.507 1.52-.608 2.432Zm17.18 10.716v-18.468h3.116v18.468h-3.116Zm1.596-22.268c-.608 0-1.128-.19-1.558-.57-.406-.38-.608-.862-.608-1.444 0-.608.202-1.09.608-1.444.43-.38.95-.57 1.558-.57.608 0 1.114.19 1.52.57.43.354.646.836.646 1.444 0 .582-.216 1.064-.646 1.444-.406.38-.912.57-1.52.57Zm7.755 22.268v-18.468h2.584l.266 2.66h.114a14.7 14.7 0 0 1 2.774-2.204c.988-.608 2.128-.912 3.42-.912 1.951 0 3.37.608 4.256 1.824.912 1.216 1.368 3.014 1.368 5.396v11.704h-3.116v-11.286c0-1.748-.278-3.002-.836-3.762-.557-.786-1.444-1.178-2.66-1.178-.962 0-1.811.24-2.546.722-.734.481-1.57 1.19-2.508 2.128v13.376h-3.116Zm28.267.456a9.44 9.44 0 0 1-3.496-.646 8.908 8.908 0 0 1-2.812-1.9c-.785-.836-1.406-1.85-1.862-3.04-.456-1.191-.684-2.546-.684-4.066s.228-2.876.684-4.066c.482-1.216 1.102-2.242 1.862-3.078.786-.836 1.672-1.47 2.66-1.9a7.265 7.265 0 0 1 3.078-.684c1.166 0 2.204.202 3.116.608.938.405 1.71.988 2.318 1.748.634.76 1.115 1.672 1.444 2.736.33 1.064.494 2.254.494 3.572 0 .329-.012.658-.038.988 0 .304-.025.57-.076.798h-12.464c.127 1.976.735 3.546 1.824 4.712 1.115 1.14 2.559 1.71 4.332 1.71.887 0 1.698-.127 2.432-.38.76-.279 1.482-.634 2.166-1.064l1.102 2.052c-.81.506-1.71.95-2.698 1.33-.988.38-2.115.57-3.382.57Zm-5.814-11.172h9.88c0-1.875-.405-3.294-1.216-4.256-.785-.988-1.9-1.482-3.344-1.482-.658 0-1.292.126-1.9.38a4.889 4.889 0 0 0-1.596 1.14c-.481.481-.886 1.076-1.216 1.786-.304.709-.506 1.52-.608 2.432Zm-104.71 43.77-1.177 3.8h7.486l-1.178-3.8a154.34 154.34 0 0 1-1.292-4.142c-.406-1.394-.811-2.812-1.216-4.256h-.152c-.38 1.444-.773 2.862-1.178 4.256a154.34 154.34 0 0 1-1.292 4.142Zm-7.6 13.946 8.437-24.928h3.572l8.436 24.928H76.73l-2.356-7.6h-9.082l-2.394 7.6h-3.23Zm31.843.456c-.862 0-1.761-.203-2.698-.608a10.596 10.596 0 0 1-2.584-1.748h-.114l-.266 1.9H83.34v-27.056h3.116v7.372l-.076 3.344a12.246 12.246 0 0 1 2.736-1.824c.988-.507 2.001-.76 3.04-.76 1.19 0 2.242.228 3.154.684a5.891 5.891 0 0 1 2.28 1.9c.633.81 1.102 1.798 1.406 2.964.329 1.14.494 2.419.494 3.838 0 1.57-.216 2.976-.646 4.218-.431 1.241-1.014 2.292-1.748 3.154a7.614 7.614 0 0 1-2.546 1.976 7.372 7.372 0 0 1-3.04.646Zm-.532-2.622c.76 0 1.456-.165 2.09-.494a5.083 5.083 0 0 0 1.672-1.444c.481-.634.848-1.406 1.102-2.318.278-.912.418-1.938.418-3.078 0-1.014-.089-1.938-.266-2.774-.178-.836-.469-1.546-.874-2.128a3.862 3.862 0 0 0-1.52-1.406c-.608-.33-1.343-.494-2.204-.494-1.495 0-3.142.836-4.94 2.508v9.69c.81.709 1.608 1.216 2.394 1.52.81.278 1.52.418 2.128.418Zm21.536 2.622c-.862 0-1.761-.203-2.698-.608a10.582 10.582 0 0 1-2.584-1.748h-.114l-.266 1.9h-2.508v-27.056h3.116v7.372l-.076 3.344a12.246 12.246 0 0 1 2.736-1.824c.988-.507 2.001-.76 3.04-.76 1.19 0 2.242.228 3.154.684a5.891 5.891 0 0 1 2.28 1.9c.633.81 1.102 1.798 1.406 2.964.329 1.14.494 2.419.494 3.838 0 1.57-.216 2.976-.646 4.218-.431 1.241-1.014 2.292-1.748 3.154a7.625 7.625 0 0 1-2.546 1.976 7.374 7.374 0 0 1-3.04.646Zm-.532-2.622c.76 0 1.456-.165 2.09-.494a5.09 5.09 0 0 0 1.672-1.444c.481-.634.848-1.406 1.102-2.318.278-.912.418-1.938.418-3.078 0-1.014-.089-1.938-.266-2.774-.178-.836-.469-1.546-.874-2.128a3.865 3.865 0 0 0-1.52-1.406c-.608-.33-1.343-.494-2.204-.494-1.495 0-3.142.836-4.94 2.508v9.69c.81.709 1.608 1.216 2.394 1.52.81.278 1.52.418 2.128.418Zm13.365 2.166v-18.468h3.116v18.468h-3.116Zm1.596-22.268c-.608 0-1.127-.19-1.558-.57-.405-.38-.608-.862-.608-1.444 0-.608.203-1.09.608-1.444.431-.38.95-.57 1.558-.57.608 0 1.115.19 1.52.57.431.354.646.836.646 1.444 0 .582-.215 1.064-.646 1.444-.405.38-.912.57-1.52.57Zm11.062 22.724c-1.191 0-2.039-.355-2.546-1.064-.507-.71-.76-1.748-.76-3.116v-23.332h3.116v23.56c0 .506.089.861.266 1.064.177.202.38.304.608.304h.266c.101 0 .241-.026.418-.076l.418 2.356a2.713 2.713 0 0 1-.722.228c-.279.05-.633.076-1.064.076Zm12.688 0c-2.331 0-4.193-.836-5.586-2.508-1.368-1.672-2.052-4.054-2.052-7.144 0-1.495.215-2.838.646-4.028.456-1.216 1.051-2.242 1.786-3.078a7.819 7.819 0 0 1 2.546-1.938 7.014 7.014 0 0 1 3.04-.684c1.064 0 1.988.19 2.774.57.785.38 1.583.899 2.394 1.558l-.152-3.154v-7.106h3.154v27.056h-2.584l-.266-2.166h-.114a12.026 12.026 0 0 1-2.546 1.862 6.303 6.303 0 0 1-3.04.76Zm.684-2.622c.861 0 1.672-.203 2.432-.608.76-.431 1.52-1.077 2.28-1.938v-9.652c-.786-.71-1.546-1.204-2.28-1.482a5.543 5.543 0 0 0-2.204-.456c-.735 0-1.432.177-2.09.532-.634.329-1.191.81-1.672 1.444-.482.608-.862 1.342-1.14 2.204-.279.861-.418 1.824-.418 2.888 0 2.229.443 3.964 1.33 5.206.886 1.241 2.14 1.862 3.762 1.862Zm19.482 2.622c-1.976 0-3.42-.608-4.332-1.824-.887-1.216-1.33-3.015-1.33-5.396v-11.704h3.154v11.286c0 1.748.266 3.014.798 3.8.557.76 1.444 1.14 2.66 1.14.962 0 1.811-.241 2.546-.722.734-.507 1.545-1.305 2.432-2.394v-13.11h3.116v18.468h-2.584l-.266-2.888h-.114c-.862 1.013-1.774 1.824-2.736 2.432-.938.608-2.052.912-3.344.912Zm15.274-.456v-18.468h2.584l.266 2.66h.114a14.637 14.637 0 0 1 2.774-2.204c.988-.608 2.128-.912 3.42-.912 1.95 0 3.369.608 4.256 1.824.912 1.216 1.368 3.014 1.368 5.396v11.704h-3.116v-11.286c0-1.748-.279-3.002-.836-3.762-.558-.786-1.444-1.178-2.66-1.178-.963 0-1.812.24-2.546.722-.735.481-1.571 1.19-2.508 2.128v13.376h-3.116Zm27.013 8.512c-1.14 0-2.179-.114-3.116-.342-.937-.203-1.748-.52-2.432-.95a5.076 5.076 0 0 1-1.558-1.558c-.355-.608-.532-1.318-.532-2.128 0-.786.241-1.533.722-2.242.481-.71 1.14-1.356 1.976-1.938v-.152a3.879 3.879 0 0 1-1.178-1.14c-.304-.507-.456-1.128-.456-1.862 0-.786.215-1.47.646-2.052.431-.583.887-1.039 1.368-1.368v-.152c-.608-.507-1.165-1.178-1.672-2.014-.481-.862-.722-1.85-.722-2.964 0-1.014.177-1.926.532-2.736a6.197 6.197 0 0 1 1.52-2.052 6.49 6.49 0 0 1 2.204-1.33 8.047 8.047 0 0 1 2.698-.456c.507 0 .975.05 1.406.152.456.076.861.177 1.216.304h6.422v2.394h-3.8c.431.43.785.975 1.064 1.634.304.658.456 1.38.456 2.166 0 .988-.177 1.887-.532 2.698a6.121 6.121 0 0 1-1.444 2.014c-.608.532-1.33.95-2.166 1.254a7.41 7.41 0 0 1-2.622.456c-.456 0-.925-.051-1.406-.152a8.072 8.072 0 0 1-1.368-.494 4.237 4.237 0 0 0-.836.95c-.228.329-.342.747-.342 1.254 0 .582.228 1.064.684 1.444.456.38 1.317.57 2.584.57h3.572c2.153 0 3.762.354 4.826 1.064 1.089.684 1.634 1.798 1.634 3.344 0 .861-.215 1.672-.646 2.432-.431.785-1.051 1.469-1.862 2.052-.811.582-1.799 1.038-2.964 1.368-1.14.354-2.432.532-3.876.532Zm0-16.454c.532 0 1.026-.102 1.482-.304a3.625 3.625 0 0 0 1.254-.874c.38-.38.671-.836.874-1.368a5.412 5.412 0 0 0 .304-1.862c0-1.368-.38-2.42-1.14-3.154-.76-.76-1.685-1.14-2.774-1.14s-2.014.38-2.774 1.14c-.76.734-1.14 1.786-1.14 3.154 0 .684.101 1.304.304 1.862.203.532.481.988.836 1.368.38.38.798.671 1.254.874.481.202.988.304 1.52.304Zm.456 14.288c.887 0 1.685-.114 2.394-.342.735-.203 1.355-.482 1.862-.836.507-.33.899-.722 1.178-1.178.279-.456.418-.925.418-1.406 0-.862-.317-1.457-.95-1.786-.633-.33-1.558-.494-2.774-.494h-3.192c-.355 0-.747-.026-1.178-.076a5.209 5.209 0 0 1-1.216-.228c-.659.481-1.14.988-1.444 1.52-.304.532-.456 1.064-.456 1.596 0 .988.469 1.773 1.406 2.356.963.582 2.28.874 3.952.874ZM62.021 249.353l-6.65-18.468h3.23l3.496 10.488.836 2.736c.304.912.595 1.811.874 2.698h.152c.279-.887.557-1.786.836-2.698l.836-2.736 3.496-10.488h3.078l-6.536 18.468h-3.648Zm20.782.456c-1.14 0-2.23-.216-3.268-.646a8.196 8.196 0 0 1-2.698-1.862c-.785-.836-1.419-1.85-1.9-3.04-.456-1.216-.684-2.584-.684-4.104 0-1.546.228-2.914.684-4.104.481-1.216 1.115-2.242 1.9-3.078a7.92 7.92 0 0 1 2.698-1.9 8.443 8.443 0 0 1 3.268-.646c1.14 0 2.216.215 3.23.646a7.828 7.828 0 0 1 2.736 1.9c.785.836 1.406 1.862 1.862 3.078.481 1.19.722 2.558.722 4.104 0 1.52-.24 2.888-.722 4.104-.456 1.19-1.077 2.204-1.862 3.04a8.095 8.095 0 0 1-2.736 1.862 8.184 8.184 0 0 1-3.23.646Zm0-2.584a4.78 4.78 0 0 0 2.166-.494 5.084 5.084 0 0 0 1.672-1.444c.481-.634.849-1.381 1.102-2.242.253-.862.38-1.824.38-2.888 0-1.064-.127-2.027-.38-2.888-.253-.887-.62-1.647-1.102-2.28a4.546 4.546 0 0 0-1.672-1.444 4.492 4.492 0 0 0-2.166-.532c-.785 0-1.507.177-2.166.532a4.868 4.868 0 0 0-1.71 1.444c-.456.633-.81 1.393-1.064 2.28-.254.861-.38 1.824-.38 2.888 0 1.064.126 2.026.38 2.888.253.861.608 1.608 1.064 2.242a5.483 5.483 0 0 0 1.71 1.444c.659.329 1.38.494 2.166.494Zm13.414 2.128v-18.468H98.8l.266 3.344h.114c.633-1.166 1.393-2.09 2.28-2.774.912-.684 1.9-1.026 2.964-1.026.734 0 1.393.126 1.976.38l-.608 2.736a6.798 6.798 0 0 0-.836-.228 4.939 4.939 0 0 0-.95-.076c-.786 0-1.609.316-2.47.95-.836.633-1.571 1.735-2.204 3.306v11.856h-3.116Zm13.173 0v-27.056h3.116v7.372l-.114 3.8a16.523 16.523 0 0 1 2.774-2.128c.963-.608 2.09-.912 3.382-.912 1.951 0 3.37.608 4.256 1.824.912 1.216 1.368 3.014 1.368 5.396v11.704h-3.116v-11.286c0-1.748-.278-3.002-.836-3.762-.557-.786-1.444-1.178-2.66-1.178-.962 0-1.811.24-2.546.722-.734.481-1.57 1.19-2.508 2.128v13.376h-3.116Zm24.926.456c-1.545 0-2.837-.456-3.876-1.368-1.013-.912-1.52-2.204-1.52-3.876 0-2.027.9-3.572 2.698-4.636 1.799-1.09 4.674-1.85 8.626-2.28 0-.583-.063-1.153-.19-1.71a4 4 0 0 0-.57-1.482c-.278-.431-.671-.773-1.178-1.026-.481-.279-1.102-.418-1.862-.418a7.473 7.473 0 0 0-3.002.608c-.937.405-1.773.861-2.508 1.368l-1.216-2.166c.862-.558 1.913-1.09 3.154-1.596 1.242-.532 2.61-.798 4.104-.798 2.255 0 3.889.696 4.902 2.09 1.014 1.368 1.52 3.204 1.52 5.51v11.324h-2.584l-.266-2.204h-.114a16.403 16.403 0 0 1-2.85 1.9 7.212 7.212 0 0 1-3.268.76Zm.912-2.508c.887 0 1.723-.203 2.508-.608.786-.431 1.622-1.052 2.508-1.862v-5.13c-1.545.202-2.85.443-3.914.722-1.038.278-1.887.608-2.546.988-.633.38-1.102.823-1.406 1.33a3.133 3.133 0 0 0-.418 1.596c0 1.064.317 1.824.95 2.28.634.456 1.406.684 2.318.684Zm13.981 2.052v-18.468h2.584l.266 2.66h.114a14.637 14.637 0 0 1 2.774-2.204c.988-.608 2.128-.912 3.42-.912 1.95 0 3.369.608 4.256 1.824.912 1.216 1.368 3.014 1.368 5.396v11.704h-3.116v-11.286c0-1.748-.279-3.002-.836-3.762-.558-.786-1.444-1.178-2.66-1.178-.963 0-1.812.24-2.546.722-.735.481-1.571 1.19-2.508 2.128v13.376h-3.116Zm27.089.456c-2.331 0-4.193-.836-5.586-2.508-1.368-1.672-2.052-4.054-2.052-7.144 0-1.495.215-2.838.646-4.028.456-1.216 1.051-2.242 1.786-3.078a7.83 7.83 0 0 1 2.546-1.938 7.017 7.017 0 0 1 3.04-.684c1.064 0 1.989.19 2.774.57.785.38 1.583.899 2.394 1.558l-.152-3.154v-7.106h3.154v27.056h-2.584l-.266-2.166h-.114a12 12 0 0 1-2.546 1.862 6.3 6.3 0 0 1-3.04.76Zm.684-2.622c.861 0 1.672-.203 2.432-.608.76-.431 1.52-1.077 2.28-1.938v-9.652c-.785-.71-1.545-1.204-2.28-1.482a5.54 5.54 0 0 0-2.204-.456c-.735 0-1.431.177-2.09.532-.633.329-1.191.81-1.672 1.444-.481.608-.861 1.342-1.14 2.204-.279.861-.418 1.824-.418 2.888 0 2.229.443 3.964 1.33 5.206.887 1.241 2.141 1.862 3.762 1.862Zm21.572 2.622a9.435 9.435 0 0 1-3.496-.646 8.908 8.908 0 0 1-2.812-1.9c-.785-.836-1.406-1.85-1.862-3.04-.456-1.191-.684-2.546-.684-4.066s.228-2.876.684-4.066c.481-1.216 1.102-2.242 1.862-3.078a8.026 8.026 0 0 1 2.66-1.9 7.265 7.265 0 0 1 3.078-.684c1.165 0 2.204.202 3.116.608.937.405 1.71.988 2.318 1.748.633.76 1.115 1.672 1.444 2.736.329 1.064.494 2.254.494 3.572 0 .329-.013.658-.038.988 0 .304-.025.57-.076.798h-12.464c.127 1.976.735 3.546 1.824 4.712 1.115 1.14 2.559 1.71 4.332 1.71.887 0 1.697-.127 2.432-.38.76-.279 1.482-.634 2.166-1.064l1.102 2.052c-.811.506-1.71.95-2.698 1.33-.988.38-2.115.57-3.382.57Zm-5.814-11.172h9.88c0-1.875-.405-3.294-1.216-4.256-.785-.988-1.9-1.482-3.344-1.482a4.88 4.88 0 0 0-1.9.38 4.868 4.868 0 0 0-1.596 1.14c-.481.481-.887 1.076-1.216 1.786-.304.709-.507 1.52-.608 2.432Zm17.18 10.716v-18.468h2.584l.266 2.66h.114a14.637 14.637 0 0 1 2.774-2.204c.988-.608 2.128-.912 3.42-.912 1.95 0 3.369.608 4.256 1.824.912 1.216 1.368 3.014 1.368 5.396v11.704h-3.116v-11.286c0-1.748-.279-3.002-.836-3.762-.558-.786-1.444-1.178-2.66-1.178-.963 0-1.812.24-2.546.722-.735.481-1.571 1.19-2.508 2.128v13.376h-3.116Z"/>\n      </svg>\n    ']);return i=function(){return e},e}class c extends o.a{static get styles(){return[r]}render(){return Object(o.c)(i())}}customElements.define("dkp-product-cover-fallback",c)},function(e,t,n){"use strict";function o(e){return e.hasChanged=e=>{var t=document.documentElement,n=document.body;return e?(t.style.position="absolute",t.style.width="100%",t.style.height="100%",t.style.overflow="visible",n.style.overflow="hidden",!0):(t.style.position="",t.style.width="",t.style.height="",t.style.overflow="",n.style.overflow="",!0)},e}n.r(t),n.d(t,"disableBackgroundScrolling",(function(){return o}))},function(e,t,n){"use strict";n.r(t);var o=n(3),r=n(11),i=n.n(r),c=(n(13),o.b`:host{display:inline;--link-text--color:#007688;--link-text--hover-color:#005867;--link-text--disabled-color:#757575;--link-text--disabled-hover-color:#757575}:host([hidden]){display:none}.link-text{text-decoration:none;display:flex;background-color:rgba(0,0,0,0);border-radius:0;padding:0;border:0;vertical-align:top;color:var(--link-text--color)}.link-text:active,.link-text:hover{outline:0}.link-text:active,.link-text:focus,.link-text:hover{text-decoration:none;color:var(--link-text--hover-color)}.link-text:focus-visible{outline:3px dotted #007688;outline-offset:2px}.text{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.link-text--m{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.1875rem;font-weight:400;line-height:1.4375rem}.link-text--s{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.0625rem;font-weight:400;line-height:1.375rem}.link-text--xs{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.9375rem;font-weight:400;line-height:1.1875rem}.link-text--xxs{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.8125rem;font-weight:400;line-height:1.0625rem}.link-text--external::after,.link-text--internal::after{font-style:normal;font-weight:400;font-variant:normal;vertical-align:middle;speak:none;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;content:"";font-size:1.25rem;font-family:CV UNI Icons flat}.link-text--external:after,.link-text--internal:after{display:inline-block;font-size:inherit;margin-left:4px;vertical-align:sub;line-height:1.5rem}.link-text--external .link-text__text:active,.link-text--external .link-text__text:focus,.link-text--external .link-text__text:hover,.link-text--internal .link-text__text:active,.link-text--internal .link-text__text:focus,.link-text--internal .link-text__text:hover{text-decoration:none}.link-text--prepended-icon dkp-icon{margin-right:4px;margin-left:0}dkp-icon{margin-left:4px}.link-text--external:after{transform:rotate(-45deg)}.link-text--disabled{color:var(--link-text--disabled-color)}.link-text--disabled:hover{cursor:default;color:var(--link-text--disabled-hover-color)}`);function s(){var e=f(['\n        <a\n          @click="','"\n          class="','"\n          href="','"\n          target="','"\n        >\n            ',"\n        </a>\n    "]);return s=function(){return e},e}function a(){var e=f(['\n        <span class="','">\n            ',"\n        </span>\n      "]);return a=function(){return e},e}function l(){var e=f(['\n      <span class="text">\n        ',"\n      </span>\n    "]);return l=function(){return e},e}function d(){var e=f(["",'<dkp-icon icon="','" style="','" size="','"></dkp-icon>']);return d=function(){return e},e}function u(){var e=f(['<dkp-icon icon="','" style="','" size="','"></dkp-icon>',""]);return u=function(){return e},e}function f(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){h(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class m extends o.a{static get styles(){return[c]}static get properties(){return{disabled:{type:Boolean},"disable-links":{type:Boolean},href:{type:String},icon:{type:String},"external-link":{type:Boolean},"internal-link":{type:Boolean},size:{type:String},style:{type:String},prependedIcon:{type:Boolean},text:{type:String},"custom-event-handler":{type:Boolean}}}constructor(){super(),this.size="m",this.style="outline",this.prependedIcon=!1}useCustomEventHandler(e){var t=e.constructor;e.preventDefault(),this.dispatchEvent(new t(e.type,b(b({},e),{},{composed:!0,bubbles:!0})))}renderLinkContent(){return Object(o.c)(l(),(()=>this.icon?this.prependedIcon?Object(o.c)(u(),this.icon,this.style,this.size,this.text):Object(o.c)(d(),this.text,this.icon,this.style,this.size):this.text)())}render(){var e=Boolean(this.disabled||this["disable-links"]),t=this.icon&&!this.prependedIcon,n=i()("link-text",{["".concat("link-text","--m")]:"m"===this.size,["".concat("link-text","--s")]:"s"===this.size,["".concat("link-text","--xs")]:"xs"===this.size,["".concat("link-text","--xxs")]:"xxs"===this.size,["".concat("link-text","--disabled")]:this.disabled,["".concat("link-text","--internal")]:this["internal-link"]&&!t,["".concat("link-text","--external")]:this["external-link"]&&!t,["".concat("link-text","--prepended-icon")]:this.prependedIcon});return e?Object(o.c)(a(),n,this.renderLinkContent()):Object(o.c)(s(),this["custom-event-handler"]?this.useCustomEventHandler:void 0,n,this.href,this["external-link"]?"_blank":"",this.renderLinkContent())}}customElements.define("dkp-link-text",m)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=(n(17),n(18),o.b`:host{display:block}:host([hidden]){display:none}.product-cover{display:inherit;margin:0}.product-cover:after,.product-cover:before{content:"";display:table}.product-cover:after{clear:both}dkp-image,dkp-product-cover-fallback{display:block;width:110px;border:1px solid #e0e0e0}`);function i(){var e=a(['\n        <figure class="','">\n            ',"\n        </figure>\n    "]);return i=function(){return e},e}function c(){var e=a(["<dkp-product-cover-fallback></dkp-product-cover-fallback>"]);return c=function(){return e},e}function s(){var e=a(['<dkp-image image-uri="','" alt-text="','"></dkp-image>']);return s=function(){return e},e}function a(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}class l extends o.a{static get styles(){return[r]}static get properties(){return{"image-uri":{type:String},"alt-text":{type:String}}}getContent(){return this["image-uri"]?Object(o.c)(s(),this["image-uri"],this["alt-text"]):Object(o.c)(c())}render(){return Object(o.c)(i(),"product-cover",this.getContent())}}customElements.define("dkp-product-cover",l)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=n(12),i=o.b`:host{display:inline-block;font-size:0;--link-input--color:#333333;--link-input--border-color:#BBBBBB;--link-input--focus-background-color:#E5F4F6;--link-input--focus-border-color:#0094AA;--link-input--error-background-color:#F1D8CD;--link-input--error-border-color:#B73E06}:host([hidden]){display:none}*,:after,:before{box-sizing:border-box}input::-moz-focus-inner{border:0;padding:0}.text-input{display:inherit;width:100%;height:45px;padding:14px 15px;border:1px solid var(--link-input--border-color);border-radius:2px;font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.9375rem;font-weight:400;line-height:1.0625rem;color:var(--link-input--color)}.text-input:focus{outline:1px solid #007688;outline-offset:0;border:1px solid #007688;background-color:#e5f4f6;border-width:1px}.text-input::-moz-placeholder{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.9375rem;font-weight:400;line-height:1.0625rem;color:#757575}.text-input:-ms-input-placeholder{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.9375rem;font-weight:400;line-height:1.0625rem;color:#757575}.text-input::-ms-input-placeholder{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.9375rem;font-weight:400;line-height:1.0625rem;color:#757575}.text-input::placeholder{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.9375rem;font-weight:400;line-height:1.0625rem;color:#757575}.text-input:disabled{background-color:#e0e0e0}.text-input::-ms-clear{display:none}.text-input--invalid{background-color:var(--link-input--error-background-color);border-color:var(--link-input--error-border-color)}`,c=n(11),s=n.n(c);function a(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n      <input\n          class="','"\n          ?disabled="','"\n          name="','"\n          placeholder="','"\n          ?required="','"\n          type="','"\n          .value=','\n          @change="','"\n          @keyup="','"\n      />\n    ']);return a=function(){return e},e}class l extends o.a{static get styles(){return[i]}static get properties(){return{value:{type:String,attribute:!1},name:{type:String},required:{type:Boolean},disabled:{type:Boolean},placeholder:{type:String},type:{type:String},invalid:{type:Boolean,attribute:!1}}}constructor(){super(),this.type="text"}focus(){this.shadowRoot.firstElementChild.focus()}updateValue(e){this.value=e.target.value,this.dispatchEvent(new Event("change",{composed:!0,bubbles:!0}))}onPressEnter(e){if(13===e.keyCode){var t=new CustomEvent("onPressEnter");this.dispatchEvent(t)}}render(){var e=s()("text-input",{["".concat("text-input","--invalid")]:this.invalid});return Object(o.c)(a(),e,this.disabled,Object(r.a)(this.name||void 0),Object(r.a)(this.placeholder||void 0),this.required,this.type,Object(r.a)(this.value),this.updateValue,this.onPressEnter)}}customElements.define("dkp-text-input",l)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`h1,h2,h3,h4,h5{margin-top:0;margin-bottom:.3em;line-height:1.1;font-weight:400;color:#333;text-rendering:optimizeLegibility}article,aside,details,figcaption,figure,footer,header,hgroup,main,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block}[hidden]{display:none}html{-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%;overflow-y:scroll}button,input,select,textarea{font-size:100%;margin:0;vertical-align:baseline}optgroup{font-weight:700}button,input{line-height:normal}button,select{text-transform:none}button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}button[disabled],html input[disabled]{cursor:default}input[type=checkbox],input[type=radio]{box-sizing:border-box;padding:0}input[type=search]{-webkit-appearance:textfield;box-sizing:content-box}input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{-webkit-appearance:none}button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}html{box-sizing:border-box;overflow-x:hidden}*,:after,:before{box-sizing:inherit}[tabindex="0"]:focus-visible{outline:3px dotted #007688;outline-offset:2px}body{margin:0;font:400 1.1875rem "CV Source Sans",Helvetica,Arial,sans-serif;text-align:left;color:#595959;background:#fafafa;word-wrap:break-word;overflow-wrap:break-word}@media screen and (min-width:992px){body{font:400 1.1875rem "CV Source Sans",Helvetica,Arial,sans-serif}}p{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.1875rem;font-weight:400;line-height:1.75rem;margin:0 0 23px}b,strong{font-weight:700}small{font-size:80%}abbr[title]{border-bottom:1px dotted;cursor:help}mark{padding-left:4px;padding-right:4px;background:#0094aa;color:color-adjust(#0094aa,100%)}dfn,em,i{font-style:italic}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sup{top:-7px}sub{bottom:-4px}h1{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:2.6875rem;font-weight:700;line-height:3rem;margin-bottom:20px}h2{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.5rem;font-weight:700;line-height:2rem;margin-bottom:10px}h3{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.1875rem;font-weight:700;line-height:1.4375rem;margin-bottom:0}h4{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.0625rem;font-weight:700;line-height:1.375rem;margin-bottom:0}h5{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.9375rem;font-weight:700;line-height:1.1875rem;margin-bottom:5px}a{color:#007688;text-decoration:none;background:rgba(0,0,0,0)}a:active,a:focus,a:hover{text-decoration:underline;color:#005867}a:active,a:hover{outline:0}a:focus-visible{outline:3px dotted #007688;outline-offset:2px;outline-offset:0}p a{position:relative;margin:-6px;padding:6px;color:inherit;text-decoration:underline}p a:active,p a:focus,p a:hover{color:#007688}p a:focus-visible{outline-offset:-6px}menu{padding:0 0 0 40px;margin:0 0 23px}ol,ul{list-style:none;display:table;padding:0 0 0 40px;margin:0 0 23px}ol li,ul li{line-height:1.75rem;margin-bottom:3px}ol ol,ol ul,ul ol,ul ul{display:block;margin:0}ol ol li:first-child,ol ul li:first-child,ul ol li:first-child,ul ul li:first-child{margin-top:3px}ul{padding-left:12px}ul li{padding-left:22px;position:relative}ul li:before{content:".";position:absolute;margin-right:22px;top:4px;left:0;font-size:1.875rem;line-height:0}ol{padding-left:31px;list-style-type:decimal}ol li{padding-left:4px}ol ol{list-style-type:lower-alpha}ol ul li{padding-left:22px}dl{margin:0 0 23px}q{quotes:"„" "”" "‘" "’"}blockquote{position:relative;margin:0 0 53px;padding:0 67px;font-family:"Palatino Linotype","Book Antiqua",Palatino,serif;font-style:italic;font-size:2.375rem;line-height:1.4;font-weight:200}blockquote:after,blockquote:before{position:absolute;line-height:0;font-size:7.125rem;font-family:"Palatino Linotype","Book Antiqua",Palatino,serif}blockquote:before{content:"“";left:-5px;top:28px;padding-right:2px}blockquote:after{content:"„";bottom:15px;right:0}hr{box-sizing:content-box;display:block;height:0;padding:0;margin:45px 0 40px;border:0;border-top:2px solid #e0e0e0}embed,iframe,img,object{max-width:100%;border:0}img{font-style:italic;vertical-align:middle}img[height]{height:auto}figure{margin:0}figure:after,figure:before{content:"";display:table}figure:after{clear:both}figcaption{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.8125rem;font-weight:400;line-height:1.0625rem;color:#757575}audio,iframe,video{margin:0 0 23px}audio{min-width:65%}audio:not([controls]){display:none;height:0}svg:not(:root){overflow:hidden}@media print{body{background:#fff}}`;function i(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(["\n      ","\n    "]);return i=function(){return e},e}class c extends o.a{static get styles(){return[r]}render(){return Object(o.c)(i(),r)}createRenderRoot(){var e=document.createElement("style");return document.head.appendChild(e)}}customElements.define("dkp-base-styles",c)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`@font-face{font-family:"CV UNI Icons outline";src:url(components/assets/cv_uni_icon_linie.eot);src:url(components/assets/cv_uni_icon_linie.eot?#iefix) format("embedded-opentype"),url(components/assets/cv_uni_icon_linie.woff2) format("woff2"),url(components/assets/cv_uni_icon_linie.woff) format("woff"),url(components/assets/cv_uni_icon_linie.svg) format("svg");font-display:block}@font-face{font-family:"CV UNI Icons flat";src:url(components/assets/cv_uni_icon_flaeche.eot);src:url(components/assets/cv_uni_icon_flaeche.eot?#iefix) format("embedded-opentype"),url(components/assets/cv_uni_icon_flaeche.woff2) format("woff2"),url(components/assets/cv_uni_icon_flaeche.woff) format("woff"),url(components/assets/cv_uni_icon_flaeche.svg) format("svg");font-display:block}`;function i(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(["\n      ","\n    "]);return i=function(){return e},e}class c extends o.a{static get styles(){return[r]}render(){return Object(o.c)(i(),r)}createRenderRoot(){var e=document.createElement("style");return document.head.appendChild(e)}}customElements.define("dkp-iconfont",c)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`@font-face{font-family:"CV Source Sans";src:url(components/assets/CV_SourceSans_Light.woff2) format("woff2"),url(components/assets/CV_SourceSans_Light.woff) format("woff");font-weight:300;font-style:normal;font-display:swap}@font-face{font-family:"CV Source Sans";src:url(components/assets/CV_SourceSans_Regular.woff2) format("woff2"),url(components/assets/CV_SourceSans_Regular.woff) format("woff");font-weight:400;font-style:normal;font-display:swap}@font-face{font-family:"CV Source Sans";src:url(components/assets/CV_SourceSans_Semibold.woff2) format("woff2"),url(components/assets/CV_SourceSans_Semibold.woff) format("woff");font-weight:700;font-style:normal;font-display:swap}@font-face{font-family:"CV Source Sans";src:url(components/assets/CV_SourceSans_LightItalic.woff2) format("woff2"),url(components/assets/CV_SourceSans_LightItalic.woff) format("woff");font-weight:300;font-style:italic;font-display:swap}@font-face{font-family:"CV Source Sans";src:url(components/assets/CV_SourceSans_Italic.woff2) format("woff2"),url(components/assets/CV_SourceSans_Italic.woff) format("woff");font-weight:400;font-style:italic;font-display:swap}@font-face{font-family:"CV Source Sans";src:url(components/assets/CV_SourceSans_SemiboldItalic.woff2) format("woff2"),url(components/assets/CV_SourceSans_SemiboldItalic.woff) format("woff");font-weight:700;font-style:italic;font-display:swap}`;function i(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(["\n      ","\n    "]);return i=function(){return e},e}class c extends o.a{static get styles(){return[r]}render(){return Object(o.c)(i(),r)}createRenderRoot(){var e=document.createElement("style");return document.head.appendChild(e)}}customElements.define("dkp-typography",c)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`h1,h2{margin-top:0;margin-bottom:.3em;line-height:1.1;font-weight:400;color:#333;text-rendering:optimizeLegibility}:host{display:block}h1{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.1875rem;font-weight:700;line-height:1.4375rem;margin-bottom:0}h2{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.0625rem;font-weight:700;line-height:1.375rem;margin-bottom:0;margin-bottom:10px}p{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.0625rem;font-weight:400;line-height:1.375rem;margin:0 0 15px}`,i=n(11),c=n.n(i);function s(){var e=u(["<p>","</p>"]);return s=function(){return e},e}function a(){var e=u(["<h2>","</h2>"]);return a=function(){return e},e}function l(){var e=u(["<h1>","</h1>"]);return l=function(){return e},e}function d(){var e=u(['\n            <div class="','">          \n                ',"\n                ","\n                ","\n            </div>\n        "]);return d=function(){return e},e}function u(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}class f extends o.a{static get styles(){return[r]}static get properties(){return{title:{type:String},subtitle:{type:String},info:{type:String}}}render(){var e=c()("product-list-item-description");return Object(o.c)(d(),e,this.title?Object(o.c)(l(),this.title):"",this.subtitle?Object(o.c)(a(),this.subtitle):"",this.info?Object(o.c)(s(),this.info):"")}}customElements.define("dkp-product-list-item-description",f)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`:host{display:block}*{box-sizing:border-box}.box-header{border-bottom:1px solid #e0e0e0;padding:30px 20px;align-items:center}@media screen and (min-width:768px){.box-header{display:flex;padding:30px}}h1{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.875rem;font-weight:300;line-height:2.25rem;color:#595959;text-shadow:0 2px 14px 0 rgba(0,0,0,.15);margin:0}::slotted(*){margin-top:10px}@media screen and (min-width:480px){::slotted(*){width:auto}}@media screen and (min-width:768px){::slotted(*){margin-left:auto;margin-top:0}}`;function i(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n        <section class="','">\n            <h1>',"</h1>\n            <slot></slot>\n        </section>\n    "]);return i=function(){return e},e}class c extends o.a{static get styles(){return[r]}static get properties(){return{label:{type:String}}}render(){return Object(o.c)(i(),"box-header",this.label)}}customElements.define("dkp-box-header",c)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`:host{display:block}:host(:first-of-type) .box-content{border-top:none}*{box-sizing:border-box}.box-content{display:inherit;border-top:1px solid #e0e0e0;padding:20px}@media screen and (min-width:992px){.box-content{padding:30px}}::slotted(dkp-product-list){margin:-20px}@media screen and (min-width:992px){::slotted(dkp-product-list){margin:-30px}}`;function i(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n        <section class="','">\n            <slot></slot>\n        </section>\n    ']);return i=function(){return e},e}class c extends o.a{static get styles(){return[r]}render(){return Object(o.c)(i(),"box-content")}}customElements.define("dkp-box-content",c)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`:host{display:block}*{box-sizing:border-box}.box-footer{border-top:1px solid #e0e0e0;padding:30px 20px}`;function i(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n        <section class="','">\n            <slot></slot>\n        </section>\n    ']);return i=function(){return e},e}class c extends o.a{static get styles(){return[r]}render(){return Object(o.c)(i(),"box-footer")}}customElements.define("dkp-box-footer",c)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=n(11),i=n.n(r),c=n(19),s=o.b`:host{display:block}*{box-sizing:border-box}.dialog{position:fixed;top:0;left:0;right:0;bottom:0;display:flex;justify-content:center;align-items:center;opacity:0;z-index:-1;visibility:hidden;transition-property:opacity,visibility,z-index;transition-duration:.5s,0s,0s;transition-delay:0s,.5s,.5s}dkp-overlay{flex:none}.dialog--open{opacity:1;z-index:10000;visibility:visible;transition-delay:0s}.content-container{position:absolute;top:50%;left:50%;padding:15px;transform:translate(-50%,-50%);width:100%;height:100%}@media screen and (min-width:768px){.content-container{padding:50px 70px}}@media screen and (min-width:992px){.content-container{top:0;transform:translateX(-50%);width:780px}}.content{background:#fff;width:100%;max-height:100%;overflow:auto;position:relative;top:50%;transform:translateY(-50%)}`;function a(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n      <div class="','">\n        <dkp-overlay></dkp-overlay>\n        <div class="content-container">\n          <div class="content">\n            <slot></slot>\n          </div>\n        </div>\n      </div>\n    ']);return a=function(){return e},e}class l extends o.a{static get styles(){return[s]}static get properties(){return{isOpen:Object(c.disableBackgroundScrolling)({type:Boolean,attribute:!1})}}open(){this.isOpen=!0}close(){this.isOpen=!1}render(){var e=i()("dialog",{["".concat("dialog","--open")]:this.isOpen});return Object(o.c)(a(),e)}}customElements.define("dkp-dialog",l)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=n(11),i=n.n(r),c=o.b`:host{display:block}*{box-sizing:border-box}.flex-container{display:flex;margin-top:-10px;margin-bottom:-10px}::slotted(*){margin-top:10px;margin-bottom:10px}.flex-container--flex-direction__row{flex-direction:row}.flex-container--flex-direction__row-reverse{flex-direction:row-reverse}.flex-container--flex-direction__column{flex-direction:column}.flex-container--flex-direction__column-reverse{flex-direction:column-reverse}.flex-container--flex-wrap__nowrap{flex-wrap:nowrap}.flex-container--flex-wrap__wrap{flex-wrap:wrap}.flex-container--flex-wrap__wrap-reverse{flex-wrap:wrap-reverse}.flex-container--justify-content__flex-start{justify-content:flex-start}.flex-container--justify-content__flex-end{justify-content:flex-end}.flex-container--justify-content__center{justify-content:center}.flex-container--justify-content__space-between{justify-content:space-between}.flex-container--justify-content__space-around{justify-content:space-around}.flex-container--justify-content__space-evenly{justify-content:space-evenly}.flex-container--align-items__stretch{align-items:stretch}.flex-container--align-items__flex-start{align-items:flex-start}.flex-container--align-items__flex-end{align-items:flex-end}.flex-container--align-items__center{align-items:center}.flex-container--align-items__baseline{align-items:baseline}.flex-container--align-content__flex-start{align-content:flex-start}.flex-container--align-content__flex-end{align-content:flex-end}.flex-container--align-content__center{align-content:center}.flex-container--align-content__space-between{align-content:space-between}.flex-container--align-content__space-around{align-content:space-around}.flex-container--align-content__stretch{align-content:stretch}.flex-container--has-grid-gutter{margin-left:-15px;margin-right:-15px}.flex-container--has-grid-gutter ::slotted(*){padding-left:15px;padding-right:15px}`;function s(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n      <div class="','">\n          <slot></slot>\n      </div>\n    ']);return s=function(){return e},e}function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){d(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var u=["flex-direction","flex-wrap","justify-content","align-items","align-content"];class f extends o.a{static get styles(){return[c]}static get properties(){return l(l({},u.reduce((e,t)=>(e[t]={type:String},e),{})),{},{"grid-gutter":{type:Boolean}})}render(){var e=u.map(e=>i()({["".concat("flex-container","--").concat(e,"__").concat(this[e])]:this[e]})),t=i()("flex-container",e,{["".concat("flex-container","--has-grid-gutter")]:this["grid-gutter"]});return Object(o.c)(s(),t)}}customElements.define("dkp-flex-container",f)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`:host{display:block;--form-message--color:#757575;--form-message--error-color:#B73E06}:host([hidden]){display:none}.form-message{display:inherit;width:100%;font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.8125rem;font-weight:400;line-height:1.0625rem;color:var(--form-message--color)}::slotted(a){color:inherit!important}::slotted(a){text-decoration:underline!important}::slotted(a:hover){text-decoration:none!important}::slotted(a:active){text-decoration:none!important}::slotted(a:focus){text-decoration:none!important}.form-message--error{color:var(--form-message--error-color)}`,i=n(11),c=n.n(i);function s(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n        <span class="','">\n            <slot></slot>\n        </span>\n    ']);return s=function(){return e},e}class a extends o.a{static get styles(){return[r]}static get properties(){return{variant:{type:String}}}render(){var e=c()("form-message",{["".concat("form-message","--").concat(this.variant)]:this.variant});return Object(o.c)(s(),e)}}customElements.define("dkp-form-message",a)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=(n(13),o.b`:host{display:block;--text-color:#B73E06;--icon-color:#0094AA}:host([hidden]){display:none}.hint{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.9375rem;font-weight:400;line-height:1.1875rem;display:inherit;color:var(--text-color);margin:0 0 10px}dkp-icon{position:relative;top:-2px;margin-left:5px;color:var(--icon-color)}`);function i(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n        <p class="','">\n            ','<dkp-icon icon="info" size="s"></dkp-icon>\n        </p>\n\n    ']);return i=function(){return e},e}class c extends o.a{static get styles(){return[r]}static get properties(){return{text:{type:String}}}render(){return Object(o.c)(i(),"hint",this.text)}}customElements.define("dkp-hint",c)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`:host{display:block}:host([hidden]){display:none}.overlay{display:inherit;position:absolute;top:0;right:0;bottom:0;left:0;background-color:rgba(51,51,51,.8);pointer-events:none}`;function i(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n        <div class="','"></div>\n    ']);return i=function(){return e},e}class c extends o.a{static get styles(){return[r]}render(){return Object(o.c)(i(),"overlay")}}customElements.define("dkp-overlay",c)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`:host{display:block}:host([hidden]){display:none}.paragraph{box-sizing:inherit;font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.1875rem;font-weight:400;line-height:1.75rem;margin:0 0 23px}::slotted(a){color:#007688;text-decoration:none;background:rgba(0,0,0,0);position:relative;margin:-6px;padding:6px}::slotted(a):active,::slotted(a):focus,::slotted(a):hover{text-decoration:underline;color:#005867}::slotted(a):active,::slotted(a):hover{outline:0}.paragraph--size-xxs{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.8125rem;font-weight:400;line-height:1.0625rem}.paragraph--size-xxs[data-em-font],[data-em-font] .paragraph--size-xxs{font-size:.8125em;line-height:1.30769em}.paragraph--size-xs{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.9375rem;font-weight:400;line-height:1.1875rem}.paragraph--size-xs[data-em-font],[data-em-font] .paragraph--size-xs{font-size:.9375em;line-height:1.26666em}.paragraph--size-s{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.0625rem;font-weight:400;line-height:1.375rem}.paragraph--size-s[data-em-font],[data-em-font] .paragraph--size-s{font-size:1.0625em;line-height:1.29411em}.paragraph--size-m{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.1875rem;font-weight:400;line-height:1.4375rem}.paragraph--size-m[data-em-font],[data-em-font] .paragraph--size-m{font-size:1.1875em;line-height:1.21052em}.paragraph--size-l{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.5rem;font-weight:700;line-height:2rem}.paragraph--size-l[data-em-font],[data-em-font] .paragraph--size-l{font-size:1.5em;line-height:1.33333em}.paragraph--size-xl{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.6875rem;font-weight:300;line-height:2rem}.paragraph--size-xl[data-em-font],[data-em-font] .paragraph--size-xl{font-size:1.6875em;line-height:1.18518em}.paragraph--size-xxl{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.875rem;font-weight:400;line-height:2.125rem}.paragraph--size-xxl[data-em-font],[data-em-font] .paragraph--size-xxl{font-size:1.875em;line-height:1.13333em}.paragraph--size-xxxl{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:2.375rem;font-weight:300;line-height:2.9375rem}.paragraph--size-xxxl[data-em-font],[data-em-font] .paragraph--size-xxxl{font-size:2.375em;line-height:1.23684em}.paragraph--size-xxxxl{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:2.6875rem;font-weight:700;line-height:3rem}.paragraph--size-xxxxl[data-em-font],[data-em-font] .paragraph--size-xxxxl{font-size:2.6875em;line-height:1.11627em}`,i=n(11),c=n.n(i);function s(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n        <p class="','" ?data-em-font="','">\n            <slot></slot>\n        </p>\n    ']);return s=function(){return e},e}class a extends o.a{static get styles(){return[r]}static get properties(){return{size:{type:String},emFont:{type:Boolean}}}render(){var e=c()("paragraph",{["".concat("paragraph","--size-").concat(this.size)]:this.size});return Object(o.c)(s(),e,this.emFont)}}customElements.define("dkp-paragraph",a)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=n(12),i=o.b`:host{display:inline-block;font-size:0;--select-box--color:#333333;--select-box--border-color:#BBBBBB;--select-box--error-background-color:#F1D8CD;--select-box--error-border-color:#B73E06}:host([hidden]){display:none}*,:after,:before{box-sizing:border-box}.select-box{position:relative}.select-box::after{font-style:normal;font-weight:400;font-variant:normal;vertical-align:middle;speak:none;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;content:"";font-size:1.25rem;font-family:CV UNI Icons flat}.select-box::after{font-size:.8125rem;margin-left:-28px;position:absolute;top:15px;pointer-events:none}.select{width:100%;-webkit-appearance:none;-moz-appearance:none;appearance:none;padding:11px 34px 10px 15px;background-color:#fff;border:1px solid var(--select-box--border-color);border-radius:2px;font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.9375rem;font-weight:400;line-height:1.0625rem;color:var(--select-box--color)}.select:focus{outline:1px solid #007688;outline-offset:0;border:1px solid #007688;background-color:#e5f4f6}.select:-moz-focusring{color:transparent;text-shadow:0 0 0 #000}.select::-ms-expand{display:none}.select-box--invalid .select{background-color:var(--select-box--error-background-color);border-color:var(--select-box--error-border-color)}`,c=n(11),s=n.n(c);function a(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n      <div class="','">\n\n        <select\n            @change="','"\n            class="select"\n            ?disabled="','"\n            name="','"\n            ?required="','"\n            ?multiple="','"\n            autocomplete="','"\n        >\n        </select>\n        <slot></slot>\n      </div>\n    ']);return a=function(){return e},e}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class f extends o.a{static get styles(){return[i]}static get properties(){return{name:{type:String},required:{type:Boolean},disabled:{type:Boolean},multiple:{type:Boolean},autocomplete:{type:String},value:{type:String,attribute:!1},invalid:{type:Boolean}}}constructor(){super(),this.shadowRoot.onslotchange=()=>this.updateDOM()}updateDOM(){var e=this.shadowRoot.querySelector("slot"),t=e.assignedElements(),n=this.shadowRoot.querySelector("select"),o=t.map(e=>e.cloneNode(!0)),r=t.find(e=>e.selected);this.value=r&&r.value||"",e.style.display="none",n.replaceChildren(...o)}handleChange(e){var t=e.constructor,n=this.querySelector("option[selected]");n&&n.removeAttribute("selected");var o=this.querySelector('option[value="'.concat(e.target.value,'"]'));o&&o.setAttribute("selected",!0),this.updateDOM(),this.dispatchEvent(new t(e.type,d(d({},e),{},{composed:!0,bubbles:!0})))}render(){var e=s()("select-box",{["".concat("select-box","--invalid")]:this.invalid});return Object(o.c)(a(),e,this.handleChange,this.disabled,Object(r.a)(this.name||void 0),this.required,this.multiple,Object(r.a)(this.autocomplete||void 0))}}customElements.define("dkp-select-box",f)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=n(11),i=n.n(r),c=o.b`:host{display:inline-block;--toggle--background-color:#BBBBBB;--toggle--background-color-disabled:#EAEAEA;--toggle--background-color-checked:#0094AA;--toggle--background-color-disabled-checked:#9AD5E3;--toggle--border-color-checked:#007688;--toggle--border-color-disabled-checked:#6DBAD2}:host([hidden]){display:none}.toggle{display:inherit;position:relative;box-sizing:border-box;width:34px;height:16px;border-radius:10px;cursor:pointer;background-color:var(--toggle--background-color);border:1px solid transparent;transition:.2s ease-in}.toggle:before{border-radius:50%;position:absolute;content:"";width:14px;height:14px;left:0;bottom:0;background-color:#fff;transition:.4s;box-shadow:1px 1px 5px rgba(0,0,0,.3)}.toggle--disabled{cursor:not-allowed;background-color:var(--toggle--background-color-disabled)}.toggle--disabled:before{box-shadow:none}.toggle--checked{background-color:var(--toggle--background-color-checked);border-color:var(--toggle--border-color-checked)}.toggle--checked::before{transform:translateX(18px)}.toggle--checked.toggle--disabled{background-color:var(--toggle--background-color-disabled-checked);border-color:var(--toggle--border-color-disabled-checked)}.toggle__input{opacity:0;width:0;height:0;margin:0}.toggle__input:not(:disabled):focus-visible+.toggle{outline:3px dotted #007688;outline-offset:2px}.toggle__label{width:34px;display:flex}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);border:0}`;function s(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n      <label class="','__label" @change="','">\n        <span class="sr-only">','</span>\n        <input type="checkbox" class="','__input" ?checked="','" ?disabled="','"/>\n        <span class="','"></span>\n      </label>\n    ']);return s=function(){return e},e}function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){d(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class u extends o.a{static get styles(){return[c]}static get properties(){return{checked:{type:Boolean,reflect:!0},disabled:{type:Boolean},label:{type:String}}}constructor(){super()}changeValue(e){this.disabled||(this.checked=!this.checked);var t=e.constructor;this.dispatchEvent(new t(e.type,l(l({},e),{},{composed:!0,bubbles:!0})))}render(){var e,t=i()("toggle",{["".concat("toggle","--checked")]:this.checked,["".concat("toggle","--disabled")]:this.disabled});return Object(o.c)(s(),"toggle",this.changeValue,null!==(e=this.label)&&void 0!==e?e:"toggle","toggle",this.checked,this.disabled,t)}}customElements.define("dkp-toggle",u)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=n(12),i=(n(16),n(22),o.b`:host{display:block;--activation-form--background-color:#F5F5F5;--activation-form__label--color:#595959}:host([hidden]),slot[hidden]{display:none}*{box-sizing:border-box}.activation-form{display:inherit;width:100%}@media screen and (min-width:992px){.activation-form{display:flex;flex-wrap:wrap;align-items:center}}label{display:block;font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.1875rem;font-weight:700;line-height:1.4375rem;color:var(--activation-form__label--color);margin-bottom:20px;cursor:pointer;flex:0 0 auto;order:1}@media screen and (min-width:992px){label{margin-bottom:0;width:200px}}dkp-text-input{display:block;flex:1 1 auto;order:2}@media screen and (min-width:992px){dkp-text-input{margin-right:10px}}::slotted(dkp-form-message){margin-top:10px;order:3}@media screen and (min-width:992px){::slotted(dkp-form-message){order:4;margin-left:200px}}dkp-link-button{margin-top:30px;display:block;order:4}@media screen and (min-width:768px){dkp-link-button{max-width:360px}}@media screen and (min-width:992px){dkp-link-button{margin-top:0;order:3}}`);function c(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n      <dkp-box-module>\n        <dkp-box-content>\n          <form class="','" @onSubmit="','" onsubmit="event.returnValue = false;" action="#">\n              <label for="input" @click="','">\n                  Produkt freischalten\n              </label>\n              <dkp-text-input\n                id="input"\n                type="text"\n                placeholder="','"\n                .value="','"\n                .invalid="','"\n                ?disabled="','"\n                @onPressEnter="','"\n              ></dkp-text-input>\n\n              <slot ?hidden="','"></slot>\n\n              <dkp-link-button\n                label="Weiter"\n                button\n                ?disabled="','"\n                @click="','"\n              ></dkp-link-button>\n          </form>\n        </dkp-box-content>\n      </dkp-box-module>\n    ']);return c=function(){return e},e}class s extends o.a{static get styles(){return[i]}static get properties(){return{invalid:{type:Boolean,attribute:!1},disabled:{type:Boolean},placeholder:{type:String},predefinedLicenseCode:{type:String,attribute:!1}}}constructor(){super()}onLabelClick(e){var t=e.target.getAttribute("for");this.shadowRoot.getElementById(t).focus()}onSubmitLicenseCode(e){e.preventDefault();var t=new CustomEvent("onEnterLicenseCode",{detail:{licenseCode:this.shadowRoot.getElementById("input").value}});this.dispatchEvent(t)}onSubmitForm(e){e.preventDefault?e.preventDefault():e.returnValue=!1}render(){return Object(o.c)(c(),"activation-form",this.onSubmitForm,this.onLabelClick,Object(r.a)(this.placeholder),Object(r.a)(this.predefinedLicenseCode),this.invalid,this.disabled,this.onSubmitLicenseCode,!this.invalid,this.disabled,this.onSubmitLicenseCode)}}customElements.define("dkp-activation-form",s)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`:host{display:block;width:100%}*{box-sizing:border-box}.consentOverlay{background-color:#333;width:100%;height:100%;display:flex;justify-content:center;align-items:center}dkp-icon{color:#fff}.flex-container{display:flex;flex-wrap:wrap;width:100%;max-width:780px;padding:30px}@media screen and (min-width:480px){.flex-container{flex-wrap:nowrap}}.icon-container{flex:1 0 100%;padding-bottom:20px}@media screen and (min-width:480px){.icon-container{flex:0;padding-bottom:0;padding-right:40px}}.content-container{flex:1 0 100%;display:flex;flex-wrap:wrap;justify-content:flex-end;max-width:100%}@media screen and (min-width:480px){.content-container{flex:1}}slot:not([name]){flex:1 0 100%}slot:not([name])::slotted(*){color:#fff;width:100%}slot[name=actions]{display:flex;flex-direction:column;flex:1 0 100%;max-width:100%}@media screen and (min-width:480px){slot[name=actions]{flex:0 0 auto}}slot[name=actions]::slotted(dkp-link-button){--link-button--background-color:#007688;--link-button--hover-background-color:#005867;--link-button--hover-border-color:#005867;--link-button--secondary-color:#FFFFFF;--link-button--secondary-background-color:transparent;--link-button--secondary-border-color:#FFFFFF;--link-button--secondary-hover-color:#9E9E9E;--link-button--secondary-hover-border-color:#9E9E9E;margin-bottom:20px}slot[name=actions]::slotted(dkp-link-button:last-child){margin-bottom:0}`,i=n(11),c=n.n(i);function s(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n      <section class="','">\n        <div class="flex-container">\n            <div class="icon-container">\n              <dkp-icon icon="','" size="xl" ?emFont="','"></dkp-icon>\n            </div>\n            <div class="content-container">\n              <slot></slot>\n              <slot name="actions"></slot>\n            </div>\n        </div>\n      </section>\n    ']);return s=function(){return e},e}class a extends o.a{static get styles(){return[r]}static get properties(){return{icon:{type:String},emFont:{type:Boolean}}}render(){var e=c()("consentOverlay");return Object(o.c)(s(),e,this.icon,this.emFont)}}customElements.define("dkp-consent-overlay",a)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`:host{display:block}*{box-sizing:border-box}ul{display:block!important;padding-left:0!important;margin-bottom:0!important;list-style:none!important}::slotted(li){padding:0;margin:0;margin-top:5px;list-style:none;position:relative}::slotted(li:before){content:none!important}::slotted(li:first-of-type){margin-top:0!important}`;function i(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(["\n    <ul>\n        <slot></slot>\n    </ul>\n    "]);return i=function(){return e},e}class c extends o.a{static get styles(){return[r]}render(){return Object(o.c)(i())}}customElements.define("dkp-link-list",c)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`:host{display:block}*{box-sizing:border-box}dkp-image{width:261px;flex:0 0 auto}div{flex:1 1 100%}@media screen and (min-width:992px){div{flex:1 1 400px}}.infoBox--has-image-shadow dkp-image{display:block;margin:5px -5px;width:271px}`,i=n(11),c=n.n(i);function s(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n      <article class="','">\n        <dkp-flex-container flex-wrap="wrap-reverse" grid-gutter>\n            <dkp-image image-uri="','" alt-text="','"></dkp-image>\n            <div>\n                <slot></slot>\n            </div>\n        </dkp-flex-container>\n      </article>\n    ']);return s=function(){return e},e}class a extends o.a{static get styles(){return[r]}static get properties(){return{"image-uri":{type:String},"alt-text":{type:String},"image-shadow":{type:Boolean}}}render(){var e=c()("infoBox",{["".concat("infoBox","--has-image-shadow")]:this["image-shadow"]});return Object(o.c)(s(),e,this["image-uri"],this["alt-text"])}}customElements.define("dkp-info-box",a)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=(n(20),n(15),n(14)),i=o.b`:host{display:block}:host([hidden]){display:none}.product-item{display:inherit}p{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:.9375rem;font-weight:400;line-height:1.1875rem;margin:0}`;function c(){var e=u(['\n      <div class="','">\n        <slot>\n          ',"\n          ","\n        </slot>\n      </div>\n    "]);return c=function(){return e},e}function s(){var e=u(['\n        <dkp-link-text text="Jetzt neuen Lizenzcode erwerben" href="','" size="s" internal-link></dkp-link-text>\n    ']);return s=function(){return e},e}function a(){var e=u(['\n          <dkp-hint text="Lizenz ist abgelaufen (am ',')"></dkp-hint>\n      ']);return a=function(){return e},e}function l(){var e=u(["\n        <p>\n          "," "," Lizenzlaufzeit (bis ",")\n        </p>\n      "]);return l=function(){return e},e}function d(){var e=u(["\n        <p>\n          Ihre Lizenz läuft heute ab.\n        </p>\n        "]);return d=function(){return e},e}function u(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}class f extends o.a{static get styles(){return[i]}static get properties(){return{"license-valid":{type:Boolean},"license-expiration-date":{type:String},"product-uri":{type:String}}}renderLicenseInfo(){var e=this["license-expiration-date"]?new Date(this["license-expiration-date"]):void 0,t=e?Object(r.formatDate)(e):null;if(this["license-valid"]&&e){var n=Object(r.isToday)(e)?0:Object(r.remainingTimeInDays)(e);return 0===n?Object(o.c)(d()):Object(o.c)(l(),n,n>1?"Tage":"Tag",t)}if(e)return Object(o.c)(a(),t)}renderLicenseLink(){if(!this["license-valid"]&&this["product-uri"])return Object(o.c)(s(),this["product-uri"])}render(){return Object(o.c)(c(),"product-item-license",this.renderLicenseInfo(),this.renderLicenseLink())}}customElements.define("dkp-product-item-license",f)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=n(12),i=(n(15),n(16),n(21),o.b`.description h1,.description h2{margin-top:0;margin-bottom:.3em;line-height:1.1;font-weight:400;color:#333;text-rendering:optimizeLegibility}:host{display:block}:host([hidden]){display:none}*{box-sizing:border-box}.product-list-item{display:block;width:100%;padding:30px 20px;position:relative}@media screen and (min-width:992px){.product-list-item{padding:30px}}.description h1{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.1875rem;font-weight:700;line-height:1.4375rem;margin-bottom:0}.description h2{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.0625rem;font-weight:700;line-height:1.375rem;margin-bottom:0;margin-bottom:10px}.description p{font-family:"CV Source Sans",Helvetica,Arial,sans-serif;font-size:1.0625rem;font-weight:400;line-height:1.375rem;margin:0 0 15px}.item-information{position:relative}@media screen and (min-width:992px){.item-information{display:flex;flex-flow:row;align-items:stretch}}.description{margin-bottom:30px}@media screen and (min-width:992px){.description{flex:1 1 100%;order:2;margin-bottom:0}}.image{margin-bottom:30px}@media screen and (min-width:992px){.image{flex:none;margin-right:30px;margin-bottom:0;order:1}}@media screen and (min-width:992px){.actions{display:flex;flex-direction:column;justify-content:flex-end;margin-left:30px;order:3}}.error-container{margin-top:10px}@media screen and (min-width:992px){.error-container{margin-left:140px}}dkp-badge{margin-bottom:10px;margin-right:5px}slot[name=actions]::slotted(dkp-link-button){width:100%;display:block;margin-top:10px}`);function c(){var e=a(['\n        <article class="','">\n          <div class="item-information">\n            <div class="description">\n                ','\n                <slot name="description"></slot>\n            </div>\n            <dkp-product-cover\n              class="image"\n              image-uri="','"\n              alt-text="','"\n            >\n            </dkp-product-cover>\n\n            <div class="actions">\n                <slot name="actions"></slot>\n            </div>\n          </div>\n          <div class="error-container">\n            <slot name="error"></slot>\n          </div>\n        </article>\n    ']);return c=function(){return e},e}function s(){var e=a(['<dkp-badge label="','"></dkp-badge>']);return s=function(){return e},e}function a(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}class l extends o.a{static get styles(){return[i]}static get properties(){return{"image-uri":{type:String},badges:{type:Array}}}renderBadges(){if(Array.isArray(this.badges)&&this.badges.length)return this.badges.map(e=>Object(o.c)(s(),e))}render(){return Object(o.c)(c(),"product-list-item",this.renderBadges(),Object(r.a)(this["image-uri"]||void 0),this.title)}}customElements.define("dkp-product-list-item",l)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`:host{display:block}*{box-sizing:border-box}.box-module{border:1px solid #e0e0e0;background-color:#fff}`;function i(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['\n        <section class="','">\n            <slot name="box-header"></slot>\n            <slot></slot>\n            <slot name="box-footer"></slot>\n        </section>\n    ']);return i=function(){return e},e}class c extends o.a{static get styles(){return[r]}render(){return Object(o.c)(i(),"box-module")}}customElements.define("dkp-box-module",c)},function(e,t,n){"use strict";n.r(t);var o=n(3),r=o.b`:host{display:block}*{box-sizing:border-box}::slotted(dkp-product-list-item){border-bottom:1px solid #e0e0e0}::slotted(dkp-product-list-item:last-child){border-bottom:none}`;function i(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(["\n      <slot></slot>\n    "]);return i=function(){return e},e}class c extends o.a{static get styles(){return[r]}static get properties(){return{label:{type:String}}}render(){return Object(o.c)(i())}}customElements.define("dkp-product-list",c)},function(e,t,n){"use strict";n.r(t);n(23),n(24),n(25),n(26),n(27),n(28),n(29),n(15),n(30),n(31),n(32),n(33),n(13),n(17),n(20),n(16),n(34),n(35),n(21),n(36),n(22),n(37),n(38),n(39),n(40),n(41),n(42),n(43),n(44),n(45)}]);
//# sourceMappingURL=index.js.map