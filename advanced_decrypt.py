#!/usr/bin/env python3
"""
Advanced PDF Decryption Script for myOfflineBooks

This script attempts to reverse-engineer the encryption used by myOfflineBooks
by analyzing the consistent header pattern and trying various decryption approaches.
"""

import os
import struct
from pathlib import Path
import hashlib

def analyze_file_structure(file_path):
    """Analyze the structure of the encrypted file."""
    with open(file_path, 'rb') as f:
        data = f.read()
    
    print(f"\n{'='*60}")
    print(f"ANALYZING: {file_path}")
    print(f"{'='*60}")
    print(f"File size: {len(data):,} bytes")
    
    # Analyze the header pattern
    header = data[:64]
    print(f"First 64 bytes (hex): {header.hex()}")
    
    # Look for repeating patterns
    print("\nLooking for repeating patterns:")
    for pattern_size in [4, 8, 16, 32]:
        for i in range(0, min(64, len(data) - pattern_size), pattern_size):
            pattern = data[i:i+pattern_size]
            # Count occurrences of this pattern in the first 1KB
            count = 0
            search_data = data[:1024]
            for j in range(0, len(search_data) - pattern_size + 1):
                if search_data[j:j+pattern_size] == pattern:
                    count += 1
            if count > 2:  # Pattern appears more than twice
                print(f"  Pattern at offset {i}: {pattern.hex()} (appears {count} times)")
    
    # Look for potential PDF markers that might be encrypted
    print("\nSearching for potential encrypted PDF markers:")
    pdf_markers = [b'%PDF', b'obj', b'endobj', b'stream', b'endstream', b'xref', b'trailer', b'startxref']
    
    for marker in pdf_markers:
        # Try simple XOR with the header pattern
        header_pattern = bytes.fromhex('c28038ed2d3534dd9babece024c93ebc')
        for offset in range(len(header_pattern)):
            key = header_pattern[offset:] + header_pattern[:offset]
            decrypted_start = bytes(a ^ b for a, b in zip(data[:len(marker)], key[:len(marker)]))
            if marker in decrypted_start:
                print(f"  Found potential {marker} at offset 0 with XOR key starting at {offset}")
    
    # Analyze entropy to find potential encrypted sections
    print("\nAnalyzing entropy in different sections:")
    chunk_size = 1024
    for i in range(0, min(len(data), 10240), chunk_size):
        chunk = data[i:i+chunk_size]
        if len(chunk) < chunk_size:
            break
        
        # Calculate byte frequency
        freq = [0] * 256
        for byte in chunk:
            freq[byte] += 1
        
        # Calculate entropy
        import math
        entropy = 0
        for f in freq:
            if f > 0:
                p = f / len(chunk)
                entropy -= p * math.log2(p)
        
        print(f"  Bytes {i:5d}-{i+chunk_size-1:5d}: entropy = {entropy:.2f}")
    
    return data

def try_pattern_based_decryption(data, output_path):
    """Try decryption based on the consistent header pattern."""
    header_pattern = bytes.fromhex('c28038ed2d3534dd9babece024c93ebc')
    
    print(f"\nTrying pattern-based decryption approaches:")
    
    # Method 1: XOR with repeating header pattern
    print("1. XOR with repeating header pattern...")
    key = header_pattern
    decrypted = bytearray()
    for i, byte in enumerate(data):
        decrypted.append(byte ^ key[i % len(key)])
    
    if decrypted.startswith(b'%PDF'):
        print("   ✓ SUCCESS! XOR with header pattern worked!")
        with open(output_path, 'wb') as f:
            f.write(decrypted)
        return True
    
    # Method 2: XOR with inverted header pattern
    print("2. XOR with inverted header pattern...")
    key = bytes(~b & 0xFF for b in header_pattern)
    decrypted = bytearray()
    for i, byte in enumerate(data):
        decrypted.append(byte ^ key[i % len(key)])
    
    if decrypted.startswith(b'%PDF'):
        print("   ✓ SUCCESS! XOR with inverted header pattern worked!")
        with open(output_path, 'wb') as f:
            f.write(decrypted)
        return True
    
    # Method 3: Subtract header pattern
    print("3. Subtract header pattern...")
    key = header_pattern
    decrypted = bytearray()
    for i, byte in enumerate(data):
        decrypted.append((byte - key[i % len(key)]) & 0xFF)
    
    if decrypted.startswith(b'%PDF'):
        print("   ✓ SUCCESS! Subtraction with header pattern worked!")
        with open(output_path, 'wb') as f:
            f.write(decrypted)
        return True
    
    # Method 4: Add header pattern
    print("4. Add header pattern...")
    key = header_pattern
    decrypted = bytearray()
    for i, byte in enumerate(data):
        decrypted.append((byte + key[i % len(key)]) & 0xFF)
    
    if decrypted.startswith(b'%PDF'):
        print("   ✓ SUCCESS! Addition with header pattern worked!")
        with open(output_path, 'wb') as f:
            f.write(decrypted)
        return True
    
    # Method 5: Try different rotations of the header pattern
    print("5. Trying rotated header patterns...")
    for rotation in range(1, len(header_pattern)):
        key = header_pattern[rotation:] + header_pattern[:rotation]
        decrypted = bytearray()
        for i, byte in enumerate(data):
            decrypted.append(byte ^ key[i % len(key)])
        
        if decrypted.startswith(b'%PDF'):
            print(f"   ✓ SUCCESS! XOR with header pattern rotated by {rotation} worked!")
            with open(output_path, 'wb') as f:
                f.write(decrypted)
            return True
    
    # Method 6: Try using parts of the header as key
    print("6. Trying partial header patterns...")
    for key_len in [4, 8, 12, 16]:
        if key_len <= len(header_pattern):
            key = header_pattern[:key_len]
            decrypted = bytearray()
            for i, byte in enumerate(data):
                decrypted.append(byte ^ key[i % len(key)])
            
            if decrypted.startswith(b'%PDF'):
                print(f"   ✓ SUCCESS! XOR with first {key_len} bytes of header worked!")
                with open(output_path, 'wb') as f:
                    f.write(decrypted)
                return True
    
    print("   ✗ No pattern-based decryption worked")
    return False

def try_mathematical_transforms(data, output_path):
    """Try various mathematical transformations."""
    print(f"\nTrying mathematical transformations:")
    
    # Method 1: Bit rotation
    print("1. Trying bit rotations...")
    for rotation in range(1, 8):
        decrypted = bytearray()
        for byte in data:
            # Rotate bits left
            rotated = ((byte << rotation) | (byte >> (8 - rotation))) & 0xFF
            decrypted.append(rotated)
        
        if decrypted.startswith(b'%PDF'):
            print(f"   ✓ SUCCESS! Left bit rotation by {rotation} worked!")
            with open(output_path, 'wb') as f:
                f.write(decrypted)
            return True
        
        # Rotate bits right
        decrypted = bytearray()
        for byte in data:
            rotated = ((byte >> rotation) | (byte << (8 - rotation))) & 0xFF
            decrypted.append(rotated)
        
        if decrypted.startswith(b'%PDF'):
            print(f"   ✓ SUCCESS! Right bit rotation by {rotation} worked!")
            with open(output_path, 'wb') as f:
                f.write(decrypted)
            return True
    
    # Method 2: Byte swapping
    print("2. Trying byte swapping...")
    if len(data) % 2 == 0:
        decrypted = bytearray()
        for i in range(0, len(data), 2):
            if i + 1 < len(data):
                decrypted.append(data[i + 1])
                decrypted.append(data[i])
        
        if decrypted.startswith(b'%PDF'):
            print("   ✓ SUCCESS! Byte swapping worked!")
            with open(output_path, 'wb') as f:
                f.write(decrypted)
            return True
    
    # Method 3: Nibble swapping
    print("3. Trying nibble swapping...")
    decrypted = bytearray()
    for byte in data:
        # Swap high and low nibbles
        swapped = ((byte & 0x0F) << 4) | ((byte & 0xF0) >> 4)
        decrypted.append(swapped)
    
    if decrypted.startswith(b'%PDF'):
        print("   ✓ SUCCESS! Nibble swapping worked!")
        with open(output_path, 'wb') as f:
            f.write(decrypted)
        return True
    
    print("   ✗ No mathematical transformation worked")
    return False

def main():
    print("Advanced PDF Decryption Tool for myOfflineBooks")
    print("=" * 60)
    
    # Create output directory
    output_dir = Path("decrypted_advanced")
    output_dir.mkdir(exist_ok=True)
    
    # Find encrypted PDFs
    pdf_files = list(Path("extracted_books").glob("*.pdf"))
    
    if not pdf_files:
        print("No PDF files found in extracted_books directory")
        return
    
    print(f"Found {len(pdf_files)} PDF files to analyze and decrypt")
    
    successful_decryptions = []
    
    for pdf_file in pdf_files:
        print(f"\n{'='*80}")
        print(f"PROCESSING: {pdf_file}")
        print(f"{'='*80}")
        
        # Analyze file structure
        data = analyze_file_structure(pdf_file)
        
        # Try different decryption methods
        output_path = output_dir / f"decrypted_{pdf_file.name}"
        
        success = False
        
        # Try pattern-based decryption
        if try_pattern_based_decryption(data, output_path):
            successful_decryptions.append(output_path)
            success = True
        
        # Try mathematical transformations if pattern-based failed
        if not success and try_mathematical_transforms(data, output_path):
            successful_decryptions.append(output_path)
            success = True
        
        if not success:
            print(f"\n✗ Failed to decrypt {pdf_file}")
    
    print(f"\n{'='*80}")
    print("FINAL SUMMARY")
    print(f"{'='*80}")
    print(f"Total files processed: {len(pdf_files)}")
    print(f"Successfully decrypted: {len(successful_decryptions)}")
    
    if successful_decryptions:
        print("\nDecrypted files:")
        for file_path in successful_decryptions:
            print(f"  - {file_path}")
        print(f"\nDecrypted files saved in: {output_dir}")
    else:
        print("\nNo files were successfully decrypted.")
        print("The encryption method appears to be more sophisticated than expected.")
        print("Consider:")
        print("1. The files might use a proprietary encryption algorithm")
        print("2. The key might be derived from system information or user credentials")
        print("3. The encryption might involve multiple layers or transformations")

if __name__ == "__main__":
    main()
