(function(R){typeof define=="function"&&define.amd?define(R):R()})(function(){"use strict";/*! @license DOMPurify 3.0.1 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.1/LICENSE */function R(t){return R=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},R(t)}function le(t,n){return le=Object.setPrototypeOf||function(o,i){return o.__proto__=i,o},le(t,n)}function bt(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],function(){})),!0}catch{return!1}}function q(t,n,o){return bt()?q=Reflect.construct:q=function(i,c,u){var y=[null];y.push.apply(y,c);var w=Function.bind.apply(i,y),b=new w;return u&&le(b,u.prototype),b},q.apply(null,arguments)}function vt(t,n){return Tt(t)||At(t,n)||ce(t,n)||St()}function S(t){return Nt(t)||wt(t)||ce(t)||Et()}function Nt(t){if(Array.isArray(t))return se(t)}function Tt(t){if(Array.isArray(t))return t}function wt(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function At(t,n){var o=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(o!=null){var i=[],c=!0,u=!1,y,w;try{for(o=o.call(t);!(c=(y=o.next()).done)&&(i.push(y.value),!(n&&i.length===n));c=!0);}catch(b){u=!0,w=b}finally{try{!c&&o.return!=null&&o.return()}finally{if(u)throw w}}return i}}function ce(t,n){if(t){if(typeof t=="string")return se(t,n);var o=Object.prototype.toString.call(t).slice(8,-1);if(o==="Object"&&t.constructor&&(o=t.constructor.name),o==="Map"||o==="Set")return Array.from(t);if(o==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return se(t,n)}}function se(t,n){(n==null||n>t.length)&&(n=t.length);for(var o=0,i=new Array(n);o<n;o++)i[o]=t[o];return i}function Et(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function St(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function _t(t,n){var o=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!o){if(Array.isArray(t)||(o=ce(t))||n){o&&(t=o);var i=0,c=function(){};return{s:c,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(b){throw b},f:c}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var u=!0,y=!1,w;return{s:function(){o=o.call(t)},n:function(){var b=o.next();return u=b.done,b},e:function(b){y=!0,w=b},f:function(){try{!u&&o.return!=null&&o.return()}finally{if(y)throw w}}}}var ze=Object.entries,Pe=Object.setPrototypeOf,kt=Object.isFrozen,xt=Object.getPrototypeOf,Rt=Object.getOwnPropertyDescriptor,N=Object.freeze,_=Object.seal,Ot=Object.create,Be=typeof Reflect<"u"&&Reflect,ue=Be.apply,me=Be.construct;ue||(ue=function(t,n,o){return t.apply(n,o)}),N||(N=function(t){return t}),_||(_=function(t){return t}),me||(me=function(t,n){return q(t,S(n))});var Ct=E(Array.prototype.forEach),We=E(Array.prototype.pop),W=E(Array.prototype.push),Y=E(String.prototype.toLowerCase),fe=E(String.prototype.toString),Dt=E(String.prototype.match),k=E(String.prototype.replace),Lt=E(String.prototype.indexOf),Mt=E(String.prototype.trim),A=E(RegExp.prototype.test),pe=It(TypeError);function E(t){return function(n){for(var o=arguments.length,i=new Array(o>1?o-1:0),c=1;c<o;c++)i[c-1]=arguments[c];return ue(t,n,i)}}function It(t){return function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return me(t,o)}}function l(t,n,o){o=o||Y,Pe&&Pe(t,null);for(var i=n.length;i--;){var c=n[i];if(typeof c=="string"){var u=o(c);u!==c&&(kt(n)||(n[i]=u),c=u)}t[c]=!0}return t}function I(t){var n=Ot(null),o=_t(ze(t)),i;try{for(o.s();!(i=o.n()).done;){var c=vt(i.value,2),u=c[0],y=c[1];n[u]=y}}catch(w){o.e(w)}finally{o.f()}return n}function K(t,n){for(;t!==null;){var o=Rt(t,n);if(o){if(o.get)return E(o.get);if(typeof o.value=="function")return E(o.value)}t=xt(t)}function i(c){return console.warn("fallback value for",c),null}return i}var Ge=N(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),de=N(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),he=N(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Ft=N(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),ge=N(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),Ut=N(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),$e=N(["#text"]),qe=N(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),ye=N(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Ye=N(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),V=N(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Ht=_(/\{\{[\w\W]*|[\w\W]*\}\}/gm),jt=_(/<%[\w\W]*|[\w\W]*%>/gm),zt=_(/\${[\w\W]*}/gm),Pt=_(/^data-[\-\w.\u00B7-\uFFFF]/),Bt=_(/^aria-[\-\w]+$/),Wt=_(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Gt=_(/^(?:\w+script|data):/i),$t=_(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),qt=_(/^html$/i),Yt=function(){return typeof window>"u"?null:window},Kt=function(t,n){if(R(t)!=="object"||typeof t.createPolicy!="function")return null;var o=null,i="data-tt-policy-suffix";n.currentScript&&n.currentScript.hasAttribute(i)&&(o=n.currentScript.getAttribute(i));var c="dompurify"+(o?"#"+o:"");try{return t.createPolicy(c,{createHTML:function(u){return u},createScriptURL:function(u){return u}})}catch{return console.warn("TrustedTypes policy "+c+" could not be created."),null}};function Ke(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Yt(),n=function(e){return Ke(e)};if(n.version="3.0.1",n.removed=[],!t||!t.document||t.document.nodeType!==9)return n.isSupported=!1,n;var o=t.document,i=t.document,c=t.DocumentFragment,u=t.HTMLTemplateElement,y=t.Node,w=t.Element,b=t.NodeFilter,X=t.NamedNodeMap,Ne=X===void 0?t.NamedNodeMap||t.MozNamedAttrMap:X,cn=t.HTMLFormElement,sn=t.DOMParser,Z=t.trustedTypes,Q=w.prototype,un=K(Q,"cloneNode"),mn=K(Q,"nextSibling"),fn=K(Q,"childNodes"),Te=K(Q,"parentNode");if(typeof u=="function"){var we=i.createElement("template");we.content&&we.content.ownerDocument&&(i=we.content.ownerDocument)}var x=Kt(Z,o),Ve=x?x.createHTML(""):"",J=i,Ae=J.implementation,pn=J.createNodeIterator,dn=J.createDocumentFragment,hn=J.getElementsByTagName,gn=o.importNode,O={};n.isSupported=typeof ze=="function"&&typeof Te=="function"&&Ae&&typeof Ae.createHTMLDocument<"u";var Ee=Ht,Se=jt,_e=zt,yn=Pt,bn=Bt,vn=Gt,Xe=$t,ke=Wt,f=null,Ze=l({},[].concat(S(Ge),S(de),S(he),S(ge),S($e))),p=null,Qe=l({},[].concat(S(qe),S(ye),S(Ye),S(V))),m=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),G=null,xe=null,Je=!0,Re=!0,et=!1,tt=!0,F=!1,L=!1,Oe=!1,Ce=!1,U=!1,ee=!1,te=!1,nt=!0,rt=!1,Nn="user-content-",De=!0,$=!1,H={},j=null,ot=l({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),at=null,it=l({},["audio","video","img","source","image","track"]),Le=null,lt=l({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ne="http://www.w3.org/1998/Math/MathML",re="http://www.w3.org/2000/svg",C="http://www.w3.org/1999/xhtml",z=C,Me=!1,Ie=null,Tn=l({},[ne,re,C],fe),M,wn=["application/xhtml+xml","text/html"],An="text/html",d,P=null,En=i.createElement("form"),ct=function(e){return e instanceof RegExp||e instanceof Function},Fe=function(e){P&&P===e||((!e||R(e)!=="object")&&(e={}),e=I(e),M=wn.indexOf(e.PARSER_MEDIA_TYPE)===-1?M=An:M=e.PARSER_MEDIA_TYPE,d=M==="application/xhtml+xml"?fe:Y,f="ALLOWED_TAGS"in e?l({},e.ALLOWED_TAGS,d):Ze,p="ALLOWED_ATTR"in e?l({},e.ALLOWED_ATTR,d):Qe,Ie="ALLOWED_NAMESPACES"in e?l({},e.ALLOWED_NAMESPACES,fe):Tn,Le="ADD_URI_SAFE_ATTR"in e?l(I(lt),e.ADD_URI_SAFE_ATTR,d):lt,at="ADD_DATA_URI_TAGS"in e?l(I(it),e.ADD_DATA_URI_TAGS,d):it,j="FORBID_CONTENTS"in e?l({},e.FORBID_CONTENTS,d):ot,G="FORBID_TAGS"in e?l({},e.FORBID_TAGS,d):{},xe="FORBID_ATTR"in e?l({},e.FORBID_ATTR,d):{},H="USE_PROFILES"in e?e.USE_PROFILES:!1,Je=e.ALLOW_ARIA_ATTR!==!1,Re=e.ALLOW_DATA_ATTR!==!1,et=e.ALLOW_UNKNOWN_PROTOCOLS||!1,tt=e.ALLOW_SELF_CLOSE_IN_ATTR!==!1,F=e.SAFE_FOR_TEMPLATES||!1,L=e.WHOLE_DOCUMENT||!1,U=e.RETURN_DOM||!1,ee=e.RETURN_DOM_FRAGMENT||!1,te=e.RETURN_TRUSTED_TYPE||!1,Ce=e.FORCE_BODY||!1,nt=e.SANITIZE_DOM!==!1,rt=e.SANITIZE_NAMED_PROPS||!1,De=e.KEEP_CONTENT!==!1,$=e.IN_PLACE||!1,ke=e.ALLOWED_URI_REGEXP||ke,z=e.NAMESPACE||C,m=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ct(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(m.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ct(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(m.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(m.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),F&&(Re=!1),ee&&(U=!0),H&&(f=l({},S($e)),p=[],H.html===!0&&(l(f,Ge),l(p,qe)),H.svg===!0&&(l(f,de),l(p,ye),l(p,V)),H.svgFilters===!0&&(l(f,he),l(p,ye),l(p,V)),H.mathMl===!0&&(l(f,ge),l(p,Ye),l(p,V))),e.ADD_TAGS&&(f===Ze&&(f=I(f)),l(f,e.ADD_TAGS,d)),e.ADD_ATTR&&(p===Qe&&(p=I(p)),l(p,e.ADD_ATTR,d)),e.ADD_URI_SAFE_ATTR&&l(Le,e.ADD_URI_SAFE_ATTR,d),e.FORBID_CONTENTS&&(j===ot&&(j=I(j)),l(j,e.FORBID_CONTENTS,d)),De&&(f["#text"]=!0),L&&l(f,["html","head","body"]),f.table&&(l(f,["tbody"]),delete G.tbody),N&&N(e),P=e)},st=l({},["mi","mo","mn","ms","mtext"]),ut=l({},["foreignobject","desc","title","annotation-xml"]),Sn=l({},["title","style","font","a","script"]),oe=l({},de);l(oe,he),l(oe,Ft);var Ue=l({},ge);l(Ue,Ut);var _n=function(e){var a=Te(e);(!a||!a.tagName)&&(a={namespaceURI:z,tagName:"template"});var r=Y(e.tagName),s=Y(a.tagName);return Ie[e.namespaceURI]?e.namespaceURI===re?a.namespaceURI===C?r==="svg":a.namespaceURI===ne?r==="svg"&&(s==="annotation-xml"||st[s]):!!oe[r]:e.namespaceURI===ne?a.namespaceURI===C?r==="math":a.namespaceURI===re?r==="math"&&ut[s]:!!Ue[r]:e.namespaceURI===C?a.namespaceURI===re&&!ut[s]||a.namespaceURI===ne&&!st[s]?!1:!Ue[r]&&(Sn[r]||!oe[r]):!!(M==="application/xhtml+xml"&&Ie[e.namespaceURI]):!1},B=function(e){W(n.removed,{element:e});try{e.parentNode.removeChild(e)}catch{e.remove()}},He=function(e,a){try{W(n.removed,{attribute:a.getAttributeNode(e),from:a})}catch{W(n.removed,{attribute:null,from:a})}if(a.removeAttribute(e),e==="is"&&!p[e])if(U||ee)try{B(a)}catch{}else try{a.setAttribute(e,"")}catch{}},mt=function(e){var a,r;if(Ce)e="<remove></remove>"+e;else{var s=Dt(e,/^[\r\n\t ]+/);r=s&&s[0]}M==="application/xhtml+xml"&&z===C&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var h=x?x.createHTML(e):e;if(z===C)try{a=new sn().parseFromString(h,M)}catch{}if(!a||!a.documentElement){a=Ae.createDocument(z,"template",null);try{a.documentElement.innerHTML=Me?Ve:h}catch{}}var v=a.body||a.documentElement;return e&&r&&v.insertBefore(i.createTextNode(r),v.childNodes[0]||null),z===C?hn.call(a,L?"html":"body")[0]:L?a.documentElement:v},ft=function(e){return pn.call(e.ownerDocument||e,e,b.SHOW_ELEMENT|b.SHOW_COMMENT|b.SHOW_TEXT,null,!1)},kn=function(e){return e instanceof cn&&(typeof e.nodeName!="string"||typeof e.textContent!="string"||typeof e.removeChild!="function"||!(e.attributes instanceof Ne)||typeof e.removeAttribute!="function"||typeof e.setAttribute!="function"||typeof e.namespaceURI!="string"||typeof e.insertBefore!="function"||typeof e.hasChildNodes!="function")},ae=function(e){return R(y)==="object"?e instanceof y:e&&R(e)==="object"&&typeof e.nodeType=="number"&&typeof e.nodeName=="string"},D=function(e,a,r){O[e]&&Ct(O[e],function(s){s.call(n,a,r,P)})},pt=function(e){var a;if(D("beforeSanitizeElements",e,null),kn(e))return B(e),!0;var r=d(e.nodeName);if(D("uponSanitizeElement",e,{tagName:r,allowedTags:f}),e.hasChildNodes()&&!ae(e.firstElementChild)&&(!ae(e.content)||!ae(e.content.firstElementChild))&&A(/<[/\w]/g,e.innerHTML)&&A(/<[/\w]/g,e.textContent))return B(e),!0;if(!f[r]||G[r]){if(!G[r]&&ht(r)&&(m.tagNameCheck instanceof RegExp&&A(m.tagNameCheck,r)||m.tagNameCheck instanceof Function&&m.tagNameCheck(r)))return!1;if(De&&!j[r]){var s=Te(e)||e.parentNode,h=fn(e)||e.childNodes;if(h&&s)for(var v=h.length,T=v-1;T>=0;--T)s.insertBefore(un(h[T],!0),mn(e))}return B(e),!0}return e instanceof w&&!_n(e)||(r==="noscript"||r==="noembed")&&A(/<\/no(script|embed)/i,e.innerHTML)?(B(e),!0):(F&&e.nodeType===3&&(a=e.textContent,a=k(a,Ee," "),a=k(a,Se," "),a=k(a,_e," "),e.textContent!==a&&(W(n.removed,{element:e.cloneNode()}),e.textContent=a)),D("afterSanitizeElements",e,null),!1)},dt=function(e,a,r){if(nt&&(a==="id"||a==="name")&&(r in i||r in En))return!1;if(!(Re&&!xe[a]&&A(yn,a))&&!(Je&&A(bn,a))){if(!p[a]||xe[a]){if(!(ht(e)&&(m.tagNameCheck instanceof RegExp&&A(m.tagNameCheck,e)||m.tagNameCheck instanceof Function&&m.tagNameCheck(e))&&(m.attributeNameCheck instanceof RegExp&&A(m.attributeNameCheck,a)||m.attributeNameCheck instanceof Function&&m.attributeNameCheck(a))||a==="is"&&m.allowCustomizedBuiltInElements&&(m.tagNameCheck instanceof RegExp&&A(m.tagNameCheck,r)||m.tagNameCheck instanceof Function&&m.tagNameCheck(r))))return!1}else if(!Le[a]&&!A(ke,k(r,Xe,""))&&!((a==="src"||a==="xlink:href"||a==="href")&&e!=="script"&&Lt(r,"data:")===0&&at[e])&&!(et&&!A(vn,k(r,Xe,"")))&&r)return!1}return!0},ht=function(e){return e.indexOf("-")>0},gt=function(e){var a,r,s,h;D("beforeSanitizeAttributes",e,null);var v=e.attributes;if(v){var T={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:p};for(h=v.length;h--;){a=v[h];var ie=a,g=ie.name,je=ie.namespaceURI;if(r=g==="value"?a.value:Mt(a.value),s=d(g),T.attrName=s,T.attrValue=r,T.keepAttr=!0,T.forceKeepAttr=void 0,D("uponSanitizeAttribute",e,T),r=T.attrValue,!T.forceKeepAttr&&(He(g,e),!!T.keepAttr)){if(!tt&&A(/\/>/i,r)){He(g,e);continue}F&&(r=k(r,Ee," "),r=k(r,Se," "),r=k(r,_e," "));var yt=d(e.nodeName);if(dt(yt,s,r)){if(rt&&(s==="id"||s==="name")&&(He(g,e),r=Nn+r),x&&R(Z)==="object"&&typeof Z.getAttributeType=="function"&&!je)switch(Z.getAttributeType(yt,s)){case"TrustedHTML":r=x.createHTML(r);break;case"TrustedScriptURL":r=x.createScriptURL(r);break}try{je?e.setAttributeNS(je,g,r):e.setAttribute(g,r),We(n.removed)}catch{}}}}D("afterSanitizeAttributes",e,null)}},xn=function e(a){var r,s=ft(a);for(D("beforeSanitizeShadowDOM",a,null);r=s.nextNode();)D("uponSanitizeShadowNode",r,null),!pt(r)&&(r.content instanceof c&&e(r.content),gt(r));D("afterSanitizeShadowDOM",a,null)};return n.sanitize=function(e){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r,s,h,v;if(Me=!e,Me&&(e="<!-->"),typeof e!="string"&&!ae(e)){if(typeof e.toString!="function")throw pe("toString is not a function");if(e=e.toString(),typeof e!="string")throw pe("dirty is not a string, aborting")}if(!n.isSupported)return e;if(Oe||Fe(a),n.removed=[],typeof e=="string"&&($=!1),$){if(e.nodeName){var T=d(e.nodeName);if(!f[T]||G[T])throw pe("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof y)r=mt("<!---->"),s=r.ownerDocument.importNode(e,!0),s.nodeType===1&&s.nodeName==="BODY"||s.nodeName==="HTML"?r=s:r.appendChild(s);else{if(!U&&!F&&!L&&e.indexOf("<")===-1)return x&&te?x.createHTML(e):e;if(r=mt(e),!r)return U?null:te?Ve:""}r&&Ce&&B(r.firstChild);for(var ie=ft($?e:r);h=ie.nextNode();)pt(h)||(h.content instanceof c&&xn(h.content),gt(h));if($)return e;if(U){if(ee)for(v=dn.call(r.ownerDocument);r.firstChild;)v.appendChild(r.firstChild);else v=r;return(p.shadowroot||p.shadowrootmod)&&(v=gn.call(o,v,!0)),v}var g=L?r.outerHTML:r.innerHTML;return L&&f["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&A(qt,r.ownerDocument.doctype.name)&&(g="<!DOCTYPE "+r.ownerDocument.doctype.name+`>
`+g),F&&(g=k(g,Ee," "),g=k(g,Se," "),g=k(g,_e," ")),x&&te?x.createHTML(g):g},n.setConfig=function(e){Fe(e),Oe=!0},n.clearConfig=function(){P=null,Oe=!1},n.isValidAttribute=function(e,a,r){P||Fe({});var s=d(e),h=d(a);return dt(s,h,r)},n.addHook=function(e,a){typeof a=="function"&&(O[e]=O[e]||[],W(O[e],a))},n.removeHook=function(e){if(O[e])return We(O[e])},n.removeHooks=function(e){O[e]&&(O[e]=[])},n.removeAllHooks=function(){O={}},n}var Vt=Ke(),be=(t=>(t.QA="qa",t.STAGE="stage",t.PROD="www",t))(be||{});const Xt=async t=>{const n=await(await fetch(t,{cache:"no-cache"})).text();return Vt.sanitize(n)},Zt=t=>{if(t&&!Object.values(be).includes(t))throw new Error(`${t} is not a valid environment, use 'qa', 'stage', or 'www' instead`);return`https://${t&&Object.values(be).includes(t)?t:"www"}.cornelsen.de`},Qt=async({name:t,cssFileName:n,jsFileName:o,env:i,params:c})=>{if(!t)throw new Error("No name given for renderlet to load");const u=Zt(i),y=c?`?${new URLSearchParams(c).toString()}`:"",w=await Xt(`${u}/__renderlet/${t}${y}`),b=`<link rel="stylesheet" href="${u}/_Resources/Static/Packages/Cornelsen.Webkatalog/Renderlets/normalize.css" />`,X=n?`<link rel="stylesheet" href="${u}/_Resources/Static/Packages/Cornelsen.Webkatalog/Renderlets/${n}" />`:"",Ne=o?{jsSrc:`${u}/_Resources/Static/Packages/Cornelsen.Webkatalog/Renderlets/${o}`}:{};return{content:b+X+w,...Ne}},Jt=t=>{const n=document.createElement("script");return n.src=t,n.async=!0,n},ve=async(t,n)=>{const{content:o,jsSrc:i}=await Qt(n),c=i?Jt(i):null,u=t instanceof HTMLElement?t:document.querySelector(t);if(u===null)throw new Error(`Target element not found, ${t} is not a valid selector`);u.innerHTML=o,c!==null&&u.append(c)},en={name:"compliance_footer_html",cssFileName:"ComplianceFooter.min.css",jsFileName:"ComplianceFooter.js"},tn=async(t,{env:n,params:o}={})=>{ve(t,{...en,env:n,params:o})},nn=t=>{const n={name:"footer_html",cssFileName:"Footer.min.css",jsFileName:"Footer.js"};return t.env&&(n.env=t.env),t.excludeTop===!0&&(n.params={...n.params||{},"exclude-top":"true"}),t.excludeBottom===!0&&(n.params={...n.params||{},"exclude-bottom":"true"}),n},rn=async(t,n={})=>{ve(t,nn(n))},on={name:"header_reduced_html",cssFileName:"HeaderReduced.min.css",jsFileName:"HeaderReduced.js"},an=async(t,{env:n,authToken:o}={authToken:"123456"})=>{ve(t,{...on,env:n,params:o?{authToken:o}:void 0})};function ln(){window.renderFooter=rn,window.renderComplianceFooter=tn,window.renderHeaderReduced=an}ln()});
