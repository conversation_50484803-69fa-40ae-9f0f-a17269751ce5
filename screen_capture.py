import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import time
import os
from pathlib import Path
import pyautogui
from PIL import Image, ImageDraw, ImageChops
import pynput
from pynput import keyboard
import queue

class ScreenshotTool:
    def __init__(self, root):
        self.root = root
        self.root.title("Screenshot Capture Tool")
        self.root.geometry("500x600")
        
        # Variables
        self.region = {"top": 33, "left": 736, "right": 1825, "bottom": 1523}
        self.click_point = {"x": 1355, "y": 1530}
        self.target_dir = tk.StringVar(value="./screenshots")
        self.format = tk.StringVar(value="png")
        self.jpg_quality = tk.IntVar(value=85)
        self.counter_start = tk.IntVar(value=0)
        self.is_capturing = False
        self.stop_event = threading.Event()
        self.progress_queue = queue.Queue()
        self.overlay_window = None
        self.keyboard_listener = None
        
        # Setup GUI
        self.setup_gui()
        
    def setup_gui(self):
        # Region Definition Section
        region_frame = ttk.LabelFrame(self.root, text="Screen Region", padding=10)
        region_frame.pack(fill="x", padx=10, pady=5)
        
        # Manual input fields
        coords_frame = ttk.Frame(region_frame)
        coords_frame.pack(fill="x")
        
        ttk.Label(coords_frame, text="Top:").grid(row=0, column=0, padx=5, sticky="e")
        self.top_entry = ttk.Entry(coords_frame, width=10)
        self.top_entry.grid(row=0, column=1, padx=5)
        self.top_entry.insert(0, "0")
        
        ttk.Label(coords_frame, text="Left:").grid(row=0, column=2, padx=5, sticky="e")
        self.left_entry = ttk.Entry(coords_frame, width=10)
        self.left_entry.grid(row=0, column=3, padx=5)
        self.left_entry.insert(0, "0")
        
        ttk.Label(coords_frame, text="Right:").grid(row=1, column=0, padx=5, sticky="e")
        self.right_entry = ttk.Entry(coords_frame, width=10)
        self.right_entry.grid(row=1, column=1, padx=5)
        self.right_entry.insert(0, "100")
        
        ttk.Label(coords_frame, text="Bottom:").grid(row=1, column=2, padx=5, sticky="e")
        self.bottom_entry = ttk.Entry(coords_frame, width=10)
        self.bottom_entry.grid(row=1, column=3, padx=5)
        self.bottom_entry.insert(0, "100")
        
        # Buttons for region selection
        button_frame = ttk.Frame(region_frame)
        button_frame.pack(fill="x", pady=10)
        
        ttk.Button(button_frame, text="Select Region", 
                  command=self.select_region).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Apply Manual Values", 
                  command=self.apply_manual_values).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Show Overlay", 
                  command=self.show_overlay).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Hide Overlay", 
                  command=self.hide_overlay).pack(side="left", padx=5)
        
        # Click Point Section
        click_frame = ttk.LabelFrame(self.root, text="Click Point", padding=10)
        click_frame.pack(fill="x", padx=10, pady=5)
        
        click_coords = ttk.Frame(click_frame)
        click_coords.pack(fill="x")
        
        ttk.Label(click_coords, text="X:").grid(row=0, column=0, padx=5, sticky="e")
        self.click_x_entry = ttk.Entry(click_coords, width=10)
        self.click_x_entry.grid(row=0, column=1, padx=5)
        self.click_x_entry.insert(0, "50")
        
        ttk.Label(click_coords, text="Y:").grid(row=0, column=2, padx=5, sticky="e")
        self.click_y_entry = ttk.Entry(click_coords, width=10)
        self.click_y_entry.grid(row=0, column=3, padx=5)
        self.click_y_entry.insert(0, "50")
        
        ttk.Button(click_frame, text="Select Click Point", 
                  command=self.select_click_point).pack(pady=5)
        
        # Output Settings Section
        output_frame = ttk.LabelFrame(self.root, text="Output Settings", padding=10)
        output_frame.pack(fill="x", padx=10, pady=5)
        
        # Target directory
        dir_frame = ttk.Frame(output_frame)
        dir_frame.pack(fill="x", pady=5)
        ttk.Label(dir_frame, text="Directory:").pack(side="left", padx=5)
        ttk.Entry(dir_frame, textvariable=self.target_dir, width=30).pack(side="left", padx=5)
        ttk.Button(dir_frame, text="Browse", command=self.browse_directory).pack(side="left")
        
        # Format selection
        format_frame = ttk.Frame(output_frame)
        format_frame.pack(fill="x", pady=5)
        ttk.Label(format_frame, text="Format:").pack(side="left", padx=5)
        ttk.Radiobutton(format_frame, text="PNG", variable=self.format, 
                       value="png").pack(side="left", padx=5)
        ttk.Radiobutton(format_frame, text="JPG", variable=self.format, 
                       value="jpg").pack(side="left", padx=5)
        
        # JPG quality
        quality_frame = ttk.Frame(output_frame)
        quality_frame.pack(fill="x", pady=5)
        ttk.Label(quality_frame, text="JPG Quality:").pack(side="left", padx=5)
        ttk.Scale(quality_frame, from_=1, to=100, variable=self.jpg_quality, 
                 orient="horizontal", length=200).pack(side="left", padx=5)
        self.quality_label = ttk.Label(quality_frame, text="85")
        self.quality_label.pack(side="left", padx=5)
        self.jpg_quality.trace("w", lambda *args: self.quality_label.config(
            text=str(self.jpg_quality.get())))
        
        # Counter start
        counter_frame = ttk.Frame(output_frame)
        counter_frame.pack(fill="x", pady=5)
        ttk.Label(counter_frame, text="Counter Start:").pack(side="left", padx=5)
        ttk.Entry(counter_frame, textvariable=self.counter_start, width=10).pack(side="left", padx=5)
        
        # Control Section
        control_frame = ttk.LabelFrame(self.root, text="Control", padding=10)
        control_frame.pack(fill="x", padx=10, pady=5)
        
        self.start_button = ttk.Button(control_frame, text="Start Capture", 
                                      command=self.start_capture)
        self.start_button.pack(side="left", padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="Stop Capture", 
                                     command=self.stop_capture, state="disabled")
        self.stop_button.pack(side="left", padx=5)
        
        # Progress Section
        progress_frame = ttk.LabelFrame(self.root, text="Progress", padding=10)
        progress_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.progress_text = tk.Text(progress_frame, height=10, wrap="word")
        self.progress_text.pack(fill="both", expand=True)
        
        scroll = ttk.Scrollbar(self.progress_text)
        scroll.pack(side="right", fill="y")
        self.progress_text.config(yscrollcommand=scroll.set)
        scroll.config(command=self.progress_text.yview)
        
    def select_region(self):
        self.root.withdraw()
        time.sleep(0.5)
        
        # Create a fullscreen selection window
        selection_window = tk.Toplevel()
        selection_window.attributes("-fullscreen", True)
        selection_window.attributes("-alpha", 0.3)
        selection_window.attributes("-topmost", True)
        
        canvas = tk.Canvas(selection_window, highlightthickness=0)
        canvas.pack(fill="both", expand=True)
        
        start_x = start_y = end_x = end_y = 0
        rect = None
        
        def on_press(event):
            nonlocal start_x, start_y, rect
            start_x, start_y = event.x, event.y
            if rect:
                canvas.delete(rect)
            rect = canvas.create_rectangle(start_x, start_y, start_x, start_y, 
                                         outline="red", width=2)
        
        def on_drag(event):
            nonlocal rect
            if rect:
                canvas.coords(rect, start_x, start_y, event.x, event.y)
        
        def on_release(event):
            nonlocal end_x, end_y
            end_x, end_y = event.x, event.y
            self.region["left"] = min(start_x, end_x)
            self.region["top"] = min(start_y, end_y)
            self.region["right"] = max(start_x, end_x)
            self.region["bottom"] = max(start_y, end_y)
            
            self.update_entries()
            selection_window.destroy()
            self.root.deiconify()
        
        canvas.bind("<Button-1>", on_press)
        canvas.bind("<B1-Motion>", on_drag)
        canvas.bind("<ButtonRelease-1>", on_release)
        
    def select_click_point(self):
        self.root.withdraw()
        self.log_progress("Click anywhere on screen to set click point...")
        time.sleep(0.5)
        
        # Wait for mouse click
        def on_click(x, y, button, pressed):
            if pressed:
                self.click_point["x"] = x
                self.click_point["y"] = y
                self.click_x_entry.delete(0, tk.END)
                self.click_x_entry.insert(0, str(x))
                self.click_y_entry.delete(0, tk.END)
                self.click_y_entry.insert(0, str(y))
                self.log_progress(f"Click point set to ({x}, {y})")
                return False
        
        with pynput.mouse.Listener(on_click=on_click) as listener:
            listener.join()
        
        self.root.deiconify()
        
    def apply_manual_values(self):
        try:
            self.region["top"] = int(self.top_entry.get())
            self.region["left"] = int(self.left_entry.get())
            self.region["right"] = int(self.right_entry.get())
            self.region["bottom"] = int(self.bottom_entry.get())
            self.click_point["x"] = int(self.click_x_entry.get())
            self.click_point["y"] = int(self.click_y_entry.get())
            self.log_progress("Manual values applied")
            self.show_overlay()
        except ValueError:
            messagebox.showerror("Error", "Please enter valid integer values")
            
    def update_entries(self):
        self.top_entry.delete(0, tk.END)
        self.top_entry.insert(0, str(self.region["top"]))
        self.left_entry.delete(0, tk.END)
        self.left_entry.insert(0, str(self.region["left"]))
        self.right_entry.delete(0, tk.END)
        self.right_entry.insert(0, str(self.region["right"]))
        self.bottom_entry.delete(0, tk.END)
        self.bottom_entry.insert(0, str(self.region["bottom"]))
        
    def show_overlay(self):
        if self.overlay_window:
            self.overlay_window.destroy()
            
        self.overlay_window = tk.Toplevel()
        self.overlay_window.attributes("-alpha", 0.3)
        self.overlay_window.attributes("-topmost", True)
        self.overlay_window.overrideredirect(True)
        
        width = self.region["right"] - self.region["left"]
        height = self.region["bottom"] - self.region["top"]
        
        self.overlay_window.geometry(f"{width}x{height}+{self.region['left']}+{self.region['top']}")
        self.overlay_window.configure(bg="red")
        
    def hide_overlay(self):
        if self.overlay_window:
            self.overlay_window.destroy()
            self.overlay_window = None
            
    def browse_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self.target_dir.set(directory)
            
    def log_progress(self, message):
        timestamp = time.strftime("%H:%M:%S")
        self.progress_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.progress_text.see(tk.END)
        self.root.update()
        
    def start_capture(self):
        if self.is_capturing:
            return
            
        # Validate inputs
        try:
            self.apply_manual_values()
        except:
            return
            
        # Create directory if needed
        output_dir = Path(self.target_dir.get())
        output_dir.mkdir(parents=True, exist_ok=True)
        
        self.is_capturing = True
        self.stop_event.clear()
        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")
        
        # Start keyboard listener for ESC key
        self.keyboard_listener = keyboard.Listener(on_press=self.on_key_press)
        self.keyboard_listener.start()
        
        # Hide overlay during capture
        self.hide_overlay()
        
        # Start capture thread
        capture_thread = threading.Thread(target=self.capture_loop)
        capture_thread.daemon = True
        capture_thread.start()
        
    def on_key_press(self, key):
        if key == keyboard.Key.esc and self.is_capturing:
            self.log_progress("ESC pressed - stopping capture")
            self.stop_capture()
            return False
            
    def capture_loop(self):
        counter = self.counter_start.get()
        output_dir = Path(self.target_dir.get())
        ext = self.format.get()
        
        width = self.region["right"] - self.region["left"]
        height = self.region["bottom"] - self.region["top"]
        
        same_image_count = 0
        last_image = None
        
        self.log_progress(f"Starting capture to {output_dir}")
        
        while not self.stop_event.is_set():
            try:
                # Take screenshot
                screenshot = pyautogui.screenshot(
                    region=(self.region["left"], self.region["top"], width, height)
                )
                
                # Check if image is same as last
                if last_image and self.images_are_same(screenshot, last_image):
                    same_image_count += 1
                    self.log_progress(f"Same image detected ({same_image_count}/5)")
                    if same_image_count >= 5:
                        self.log_progress("5 identical images - stopping capture")
                        break
                else:
                    same_image_count = 0
                    
                last_image = screenshot.copy()
                
                # Save screenshot
                filename = f"{counter:04d}.{ext}"
                filepath = output_dir / filename
                
                if ext == "jpg":
                    screenshot.save(filepath, "JPEG", quality=self.jpg_quality.get())
                else:
                    screenshot.save(filepath, "PNG")
                    
                self.log_progress(f"Saved: {filename}")
                counter += 1
                
                # Click at specified point
                if not self.stop_event.is_set():
                    pyautogui.click(self.click_point["x"], self.click_point["y"])
                    time.sleep(3)  # Brief delay after click
                    
            except Exception as e:
                self.log_progress(f"Error: {str(e)}")
                break
                
        self.log_progress("Capture stopped")
        self.root.after(0, self.capture_finished)
        
    def images_are_same(self, img1, img2, threshold=0):
        diff = ImageChops.difference(img1, img2)
        if diff.getbbox() is None:
            return True
        # Calculate difference percentage if needed
        return False
        
    def stop_capture(self):
        if self.is_capturing:
            self.stop_event.set()
            if self.keyboard_listener:
                self.keyboard_listener.stop()
                
    def capture_finished(self):
        self.is_capturing = False
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        
def main():
    root = tk.Tk()
    app = ScreenshotTool(root)
    root.mainloop()
    
if __name__ == "__main__":
    main()