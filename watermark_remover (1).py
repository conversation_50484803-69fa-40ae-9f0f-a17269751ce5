import cv2
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

def extract_brightness_adjustment_map(template_path):
    """
    Extract the brightness adjustment map from the watermarked blank image
    
    Args:
        template_path: Path to the blank watermarked image
    
    Returns:
        adjustment_map: Map showing how much brightness was reduced at each pixel
        watermark_mask: Binary mask of watermark areas
    """
    # Load the template image
    template = cv2.imread(template_path)
    if template is None:
        raise ValueError("Could not load template image")
    
    # Convert to grayscale for analysis
    template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY).astype(np.float32)
    
    # The background should be white (255), watermark areas are darker
    # Calculate how much brightness was reduced
    expected_background = 255.0
    adjustment_map = expected_background - template_gray
    
    # Create binary mask for watermark areas
    # Areas with significant brightness reduction
    watermark_mask = (adjustment_map > 10).astype(np.uint8) * 255
    
    # Clean up the mask
    kernel = np.ones((3,3), np.uint8)
    watermark_mask = cv2.morphologyEx(watermark_mask, cv2.MORPH_OPEN, kernel, iterations=1)
    watermark_mask = cv2.morphologyEx(watermark_mask, cv2.MORPH_CLOSE, kernel, iterations=2)
    
    # Normalize adjustment map to [0, 1] range
    adjustment_map = adjustment_map / 255.0
    
    # For color images, we'll apply the same adjustment to all channels
    adjustment_map_3d = np.stack([adjustment_map, adjustment_map, adjustment_map], axis=2)
    
    return adjustment_map_3d, watermark_mask, template

def remove_brightness_watermark(target_path, adjustment_map, watermark_mask, method='direct'):
    """
    Remove watermark by reversing the brightness adjustment
    
    Args:
        target_path: Path to the watermarked image
        adjustment_map: 3D array showing brightness reduction at each pixel
        watermark_mask: Binary mask of watermark areas
        method: Removal method ('direct', 'proportional', 'adaptive')
    
    Returns:
        cleaned_image: Image with brightness adjustment reversed
    """
    # Load target image
    target = cv2.imread(target_path).astype(np.float32)
    if target is None:
        raise ValueError("Could not load target image")
    
    # Ensure dimensions match
    if target.shape[:2] != adjustment_map.shape[:2]:
        adjustment_map = cv2.resize(adjustment_map, (target.shape[1], target.shape[0]))
        watermark_mask = cv2.resize(watermark_mask, (target.shape[1], target.shape[0]))
        adjustment_map = np.stack([adjustment_map, adjustment_map, adjustment_map], axis=2) if len(adjustment_map.shape) == 2 else adjustment_map
    
    if method == 'direct':
        # Method 1: Direct brightness addition
        # Simply add back the brightness that was removed
        cleaned = target + (adjustment_map * 255.0)
        
    elif method == 'proportional':
        # Method 2: Proportional adjustment
        # Assume the watermark was applied as: watermarked = original * (1 - alpha)
        # So: original = watermarked / (1 - alpha)
        
        alpha = adjustment_map  # Use adjustment map as alpha
        denominator = 1 - alpha
        
        # Avoid division by zero
        denominator[denominator < 0.01] = 1.0
        
        cleaned = target / denominator
        
    elif method == 'adaptive':
        # Method 3: Adaptive correction based on local content
        cleaned = target.copy()
        
        # Apply correction only in watermark areas
        mask_3d = np.stack([watermark_mask/255.0, watermark_mask/255.0, watermark_mask/255.0], axis=2)
        
        # Calculate local statistics to determine best correction
        for y in range(0, target.shape[0], 10):  # Process in blocks for efficiency
            for x in range(0, target.shape[1], 10):
                y1, y2 = y, min(y+10, target.shape[0])
                x1, x2 = x, min(x+10, target.shape[1])
                
                block = target[y1:y2, x1:x2]
                adj_block = adjustment_map[y1:y2, x1:x2]
                mask_block = mask_3d[y1:y2, x1:x2]
                
                # If block contains watermark, apply correction
                if np.any(mask_block > 0.5):
                    # Use median adjustment in the block for stability
                    correction = np.median(adj_block[mask_block[:,:,0] > 0.5]) * 255.0
                    cleaned[y1:y2, x1:x2] += correction * mask_block
    
    # Clip values to valid range [0, 255]
    cleaned = np.clip(cleaned, 0, 255).astype(np.uint8)
    
    return cleaned

def analyze_watermark_pattern(template_path):
    """
    Analyze the watermark pattern to understand the brightness adjustment
    """
    template = cv2.imread(template_path)
    template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
    
    print("Watermark Analysis:")
    print(f"Image shape: {template.shape}")
    print(f"Min pixel value: {template_gray.min()}")
    print(f"Max pixel value: {template_gray.max()}")
    print(f"Mean pixel value: {template_gray.mean():.2f}")
    
    # Find watermark pixels (significantly darker than white)
    watermark_pixels = template_gray < 240
    if np.any(watermark_pixels):
        watermark_values = template_gray[watermark_pixels]
        print(f"Watermark pixel values - Min: {watermark_values.min()}, Max: {watermark_values.max()}, Mean: {watermark_values.mean():.2f}")
        print(f"Average brightness reduction: {255 - watermark_values.mean():.2f}")
    
    return template_gray

def process_brightness_watermark(template_path, target_path, output_path=None):
    """
    Complete pipeline to remove brightness-based watermark
    
    Args:
        template_path: Path to blank watermarked template
        target_path: Path to target image with watermark
        output_path: Path to save cleaned image
    
    Returns:
        Dictionary with results from different methods
    """
    
    print("Analyzing watermark pattern...")
    analyze_watermark_pattern(template_path)
    
    print("Extracting brightness adjustment map...")
    adjustment_map, watermark_mask, template_img = extract_brightness_adjustment_map(template_path)
    
    # Try different removal methods
    methods = ['direct', 'proportional', 'adaptive']
    results = {}
    
    for method in methods:
        print(f"Trying {method} method...")
        try:
            cleaned = remove_brightness_watermark(target_path, adjustment_map, 
                                                watermark_mask, method=method)
            results[method] = cleaned
            
            # Save result if output path is provided
            if output_path:
                method_output = output_path.replace('.jpg', f'_{method}.jpg')
                cv2.imwrite(method_output, cleaned)
                print(f"Saved {method} result to {method_output}")
                
        except Exception as e:
            print(f"Method {method} failed: {e}")
            results[method] = None
    
    return results, adjustment_map, watermark_mask

def display_brightness_analysis(template_path, target_path, results, adjustment_map, watermark_mask):
    """
    Display analysis of the brightness-based watermark removal
    """
    # Load original images
    template = cv2.imread(template_path)
    target = cv2.imread(target_path)
    
    # Convert to RGB for matplotlib
    template_rgb = cv2.cvtColor(template, cv2.COLOR_BGR2RGB)
    target_rgb = cv2.cvtColor(target, cv2.COLOR_BGR2RGB)
    
    # Create visualization
    fig, axes = plt.subplots(3, 3, figsize=(15, 15))
    
    # Row 1: Original images and adjustment map
    axes[0, 0].imshow(template_rgb)
    axes[0, 0].set_title('Watermark Template')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(target_rgb)
    axes[0, 1].set_title('Target Image')
    axes[0, 1].axis('off')
    
    axes[0, 2].imshow(adjustment_map[:,:,0], cmap='hot')
    axes[0, 2].set_title('Brightness Adjustment Map')
    axes[0, 2].axis('off')
    
    # Row 2: Mask and results
    axes[1, 0].imshow(watermark_mask, cmap='gray')
    axes[1, 0].set_title('Watermark Mask')
    axes[1, 0].axis('off')
    
    methods = ['direct', 'proportional']
    for i, method in enumerate(methods):
        if method in results and results[method] is not None:
            result_rgb = cv2.cvtColor(results[method], cv2.COLOR_BGR2RGB)
            axes[1, i+1].imshow(result_rgb)
            axes[1, i+1].set_title(f'{method.capitalize()} Result')
            axes[1, i+1].axis('off')
    
    # Row 3: Comparison crops (if adaptive result exists)
    if 'adaptive' in results and results['adaptive'] is not None:
        result_rgb = cv2.cvtColor(results['adaptive'], cv2.COLOR_BGR2RGB)
        axes[2, 0].imshow(result_rgb)
        axes[2, 0].set_title('Adaptive Result')
        axes[2, 0].axis('off')
        
        # Show difference between original and cleaned
        if 'direct' in results and results['direct'] is not None:
            diff = cv2.absdiff(target, results['direct'])
            diff_rgb = cv2.cvtColor(diff, cv2.COLOR_BGR2RGB)
            axes[2, 1].imshow(diff_rgb)
            axes[2, 1].set_title('Difference (Original - Cleaned)')
            axes[2, 1].axis('off')
    
    plt.tight_layout()
    plt.show()

def clean_brightness_watermark(template_path, target_path, output_path=None):
    """
    Main function to remove brightness-based watermark
    
    Args:
        template_path: Path to blank watermarked template (002.jpg)
        target_path: Path to target image (003.jpg) 
        output_path: Path to save cleaned image (003_cleaned.jpg)
    """
    
    try:
        # Process the images
        results, adjustment_map, watermark_mask = process_brightness_watermark(
            template_path, target_path, output_path
        )
        
        # Display analysis
        display_brightness_analysis(template_path, target_path, results, 
                                  adjustment_map, watermark_mask)
        
        # Save the best result
        best_method = 'direct'  # Usually works best for brightness adjustments
        if best_method in results and results[best_method] is not None:
            if output_path and not output_path.endswith(f'_{best_method}.jpg'):
                cv2.imwrite(output_path, results[best_method])
                print(f"Saved best result to {output_path}")
            return results[best_method]
        
        return None
        
    except Exception as e:
        print(f"Error processing images: {e}")
        return None

# Example usage
if __name__ == "__main__":
    # Your actual file paths
    template_path = "downloaded_pages/template.jpg"  # Your blank watermarked image (002)
    target_path = "downloaded_pages/page113.jpg"    # Your image to be cleaned (003)
    output_path = "downloaded_pages/page113_cleaned.jpg"  # Where to save the result
    
    # Clean the image
    print("Starting brightness-based watermark removal...")
    cleaned_image = clean_brightness_watermark(template_path, target_path, output_path)
    
    if cleaned_image is not None:
        print("Successfully removed brightness-based watermark!")
        print(f"Cleaned image saved as {output_path}")
    else:
        print("Failed to remove watermark.")
