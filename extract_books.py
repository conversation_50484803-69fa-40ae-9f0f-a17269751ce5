#!/usr/bin/env python3
"""
Book Extraction Script for myOfflineBooks Application

This script extracts PDF books from the myOfflineBooks application's ZIP files.
It automatically finds all book ZIP files, extracts them, and organizes the PDFs
with proper naming based on the book metadata.
"""

import os
import zipfile
import json
import shutil
from pathlib import Path

def extract_books_from_myofflinebooks():
    """
    Extract all books from the myOfflineBooks application.
    """
    # Define paths
    uma_dir = Path("myOfflineBooks/uma")
    output_dir = Path("extracted_books")
    
    # Create output directory
    output_dir.mkdir(exist_ok=True)
    
    # Find all UMA ZIP files (these contain the actual PDFs)
    uma_zip_files = list(uma_dir.glob("*_uma.zip"))
    
    if not uma_zip_files:
        print("No UMA ZIP files found in myOfflineBooks/uma/")
        return
    
    print(f"Found {len(uma_zip_files)} book(s) to extract:")
    
    for zip_file in uma_zip_files:
        print(f"\nProcessing: {zip_file.name}")
        
        # Create temporary extraction directory
        temp_dir = Path(f"temp_{zip_file.stem}")
        temp_dir.mkdir(exist_ok=True)
        
        try:
            # Extract the ZIP file
            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # Read metadata from uma.json
            uma_json_path = temp_dir / "uma.json"
            if uma_json_path.exists():
                with open(uma_json_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                
                # Extract book information
                title = metadata.get('title', 'Unknown Title')
                isbn = metadata.get('isbnSb', 'Unknown ISBN')
                
                # Clean title for filename (remove invalid characters)
                clean_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
                clean_title = clean_title.replace(' ', '_')
                
                print(f"  Title: {title}")
                print(f"  ISBN: {isbn}")
                
                # Find PDF files in the extracted content
                pdf_files = list(temp_dir.glob("*.pdf"))
                
                if pdf_files:
                    for pdf_file in pdf_files:
                        # Create a meaningful filename
                        new_filename = f"{clean_title}_{isbn}.pdf"
                        output_path = output_dir / new_filename
                        
                        # Copy the PDF to the output directory
                        shutil.copy2(pdf_file, output_path)
                        print(f"  Extracted: {new_filename}")
                        
                        # Also save metadata as JSON
                        metadata_path = output_dir / f"{clean_title}_{isbn}_metadata.json"
                        with open(metadata_path, 'w', encoding='utf-8') as f:
                            json.dump(metadata, f, indent=2, ensure_ascii=False)
                        print(f"  Metadata saved: {metadata_path.name}")
                else:
                    print(f"  No PDF files found in {zip_file.name}")
            else:
                print(f"  No uma.json found in {zip_file.name}")
                
        except Exception as e:
            print(f"  Error processing {zip_file.name}: {e}")
        
        finally:
            # Clean up temporary directory
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
    
    print(f"\nExtraction complete! Books saved to: {output_dir.absolute()}")
    
    # List extracted files
    extracted_files = list(output_dir.glob("*.pdf"))
    if extracted_files:
        print(f"\nExtracted {len(extracted_files)} book(s):")
        for pdf_file in extracted_files:
            print(f"  - {pdf_file.name}")
    else:
        print("\nNo books were successfully extracted.")

def clean_up_temp_files():
    """Clean up any temporary extraction directories."""
    temp_dirs = [d for d in Path(".").iterdir() if d.is_dir() and d.name.startswith("extracted_book")]
    for temp_dir in temp_dirs:
        try:
            print(f"Cleaning up: {temp_dir}")
            shutil.rmtree(temp_dir)
        except PermissionError:
            print(f"  Warning: Could not remove {temp_dir} (permission denied)")
        except Exception as e:
            print(f"  Warning: Could not remove {temp_dir}: {e}")

if __name__ == "__main__":
    print("myOfflineBooks Book Extraction Tool")
    print("=" * 40)
    
    # Check if myOfflineBooks directory exists
    if not Path("myOfflineBooks/uma").exists():
        print("Error: myOfflineBooks/uma directory not found!")
        print("Please make sure you're running this script from the correct directory.")
        exit(1)
    
    # Clean up any previous temporary files
    clean_up_temp_files()
    
    # Extract the books
    extract_books_from_myofflinebooks()
    
    print("\nDone!")
