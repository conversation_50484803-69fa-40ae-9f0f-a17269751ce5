#!/usr/bin/env python3
"""
PDF Decryption Script for myOfflineBooks

This script attempts to decrypt the encrypted PDF files from myOfflineBooks
using various common encryption methods.
"""

import os
import struct
from pathlib import Path
from Crypto.Cipher import AES
from Crypto.Protocol.KDF import PBKDF2
from Crypto.Hash import SHA256
import hashlib

def try_xor_decrypt(data, key_bytes):
    """Try XOR decryption with given key bytes."""
    result = bytearray()
    key_len = len(key_bytes)
    for i, byte in enumerate(data):
        result.append(byte ^ key_bytes[i % key_len])
    return bytes(result)

def try_aes_decrypt(data, key, iv=None):
    """Try AES decryption."""
    try:
        if iv is None:
            iv = b'\x00' * 16  # Zero IV
        cipher = AES.new(key, AES.MODE_CBC, iv)
        decrypted = cipher.decrypt(data)
        return decrypted
    except Exception as e:
        return None

def extract_potential_keys_from_app():
    """Extract potential encryption keys from the application files."""
    keys = []
    
    # Common patterns that might be keys
    key_patterns = [
        b'c28038ed2d3534dd9babece024c93ebc',  # The header pattern itself
        b'myOfflineBooks',
        b'cornelsen',
        b'pspdfkit',
        b'uma',
    ]
    
    # Try to find keys in the extracted app
    app_dir = Path("extracted_app")
    if app_dir.exists():
        for js_file in app_dir.rglob("*.js"):
            try:
                with open(js_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    # Look for hex strings that might be keys
                    import re
                    hex_matches = re.findall(r'["\']([0-9a-fA-F]{32,})["\']', content)
                    for match in hex_matches:
                        if len(match) >= 32:  # At least 16 bytes
                            try:
                                key_bytes = bytes.fromhex(match)
                                if len(key_bytes) in [16, 24, 32]:  # Valid AES key lengths
                                    keys.append(key_bytes)
                            except ValueError:
                                pass
            except Exception:
                continue
    
    # Add pattern-based keys
    for pattern in key_patterns:
        keys.append(pattern[:16])  # First 16 bytes
        keys.append(pattern[:32] if len(pattern) >= 32 else pattern + b'\x00' * (32 - len(pattern)))
        
        # Hash-based keys
        keys.append(hashlib.md5(pattern).digest())
        keys.append(hashlib.sha256(pattern).digest()[:16])
        keys.append(hashlib.sha256(pattern).digest()[:32])
    
    return list(set(keys))  # Remove duplicates

def analyze_encryption_pattern(file_path):
    """Analyze the encryption pattern in the file."""
    with open(file_path, 'rb') as f:
        data = f.read(1024)  # Read first 1KB
    
    print(f"\nAnalyzing {file_path}:")
    print(f"First 64 bytes (hex): {data[:64].hex()}")
    print(f"First 32 bytes (ascii): {repr(data[:32])}")
    
    # Check for repeating patterns
    header = data[:32]
    pattern_length = 16
    for i in range(1, min(len(header) // pattern_length, 4)):
        chunk1 = header[:pattern_length]
        chunk2 = header[i*pattern_length:(i+1)*pattern_length]
        if chunk1 == chunk2:
            print(f"Repeating {pattern_length}-byte pattern found at offset {i*pattern_length}")
    
    return data

def try_decrypt_pdf(file_path, output_dir):
    """Try to decrypt a PDF file using various methods."""
    print(f"\n{'='*50}")
    print(f"Attempting to decrypt: {file_path}")
    print(f"{'='*50}")
    
    with open(file_path, 'rb') as f:
        encrypted_data = f.read()
    
    # Analyze the encryption pattern
    analyze_encryption_pattern(file_path)
    
    # Get potential keys
    keys = extract_potential_keys_from_app()
    print(f"Found {len(keys)} potential keys to try")
    
    # The consistent header pattern
    header_pattern = bytes.fromhex('c28038ed2d3534dd9babece024c93ebc')
    
    # Try different decryption methods
    methods_tried = 0
    
    # Method 1: XOR with header pattern
    print(f"\nTrying XOR decryption with header pattern...")
    xor_result = try_xor_decrypt(encrypted_data, header_pattern)
    if xor_result.startswith(b'%PDF'):
        print("✓ XOR decryption successful!")
        output_path = output_dir / f"decrypted_xor_{file_path.name}"
        with open(output_path, 'wb') as f:
            f.write(xor_result)
        return output_path
    methods_tried += 1
    
    # Method 2: Try AES with various keys
    print(f"\nTrying AES decryption with {len(keys)} keys...")
    for i, key in enumerate(keys):
        if len(key) not in [16, 24, 32]:
            continue
            
        # Try different IVs
        ivs_to_try = [
            b'\x00' * 16,  # Zero IV
            header_pattern[:16],  # Header as IV
            encrypted_data[:16],  # First 16 bytes as IV
        ]
        
        for iv in ivs_to_try:
            try:
                decrypted = try_aes_decrypt(encrypted_data, key, iv[:16])
                if decrypted and decrypted.startswith(b'%PDF'):
                    print(f"✓ AES decryption successful with key {i}!")
                    output_path = output_dir / f"decrypted_aes_{i}_{file_path.name}"
                    with open(output_path, 'wb') as f:
                        f.write(decrypted)
                    return output_path
            except Exception:
                pass
        methods_tried += 1
    
    # Method 3: Try simple byte shifting
    print(f"\nTrying byte shifting...")
    for shift in range(1, 256):
        shifted = bytes((b + shift) % 256 for b in encrypted_data)
        if shifted.startswith(b'%PDF'):
            print(f"✓ Byte shifting successful with shift {shift}!")
            output_path = output_dir / f"decrypted_shift_{shift}_{file_path.name}"
            with open(output_path, 'wb') as f:
                f.write(shifted)
            return output_path
    methods_tried += 1
    
    # Method 4: Try removing header and see if it's just obfuscated
    print(f"\nTrying header removal...")
    for header_size in [16, 32, 48, 64]:
        if len(encrypted_data) > header_size:
            without_header = encrypted_data[header_size:]
            if without_header.startswith(b'%PDF'):
                print(f"✓ Header removal successful with {header_size} bytes!")
                output_path = output_dir / f"decrypted_noheader_{header_size}_{file_path.name}"
                with open(output_path, 'wb') as f:
                    f.write(without_header)
                return output_path
    methods_tried += 1
    
    print(f"\n✗ Failed to decrypt after trying {methods_tried} different methods")
    return None

def main():
    print("PDF Decryption Tool for myOfflineBooks")
    print("=" * 50)
    
    # Create output directory
    output_dir = Path("decrypted_pdfs")
    output_dir.mkdir(exist_ok=True)
    
    # Find encrypted PDFs
    pdf_files = []
    for pdf_path in Path("extracted_books").glob("*.pdf"):
        pdf_files.append(pdf_path)
    
    if not pdf_files:
        print("No PDF files found in extracted_books directory")
        return
    
    print(f"Found {len(pdf_files)} PDF files to decrypt")
    
    successful_decryptions = []
    
    for pdf_file in pdf_files:
        result = try_decrypt_pdf(pdf_file, output_dir)
        if result:
            successful_decryptions.append(result)
    
    print(f"\n{'='*50}")
    print("SUMMARY")
    print(f"{'='*50}")
    print(f"Total files processed: {len(pdf_files)}")
    print(f"Successfully decrypted: {len(successful_decryptions)}")
    
    if successful_decryptions:
        print("\nDecrypted files:")
        for file_path in successful_decryptions:
            print(f"  - {file_path}")
    else:
        print("\nNo files were successfully decrypted.")
        print("The encryption method used might be more complex or proprietary.")

if __name__ == "__main__":
    main()
