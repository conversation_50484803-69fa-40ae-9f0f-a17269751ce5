import requests
import zipfile
import os
from pathlib import Path
import time

# Base URL pattern
base_url = "https://blickinsbuch.westermann.de/978-3-14-112485-9/content/pages/page{}.jpg"
base_url = "https://www.ccbuchner.de/_files_media/livebook/7201/preview/big/{}.jpg"
base_url = "https://blickinsbuch.westermann.de/978-3-14-150629-7/content/pages/page{}.jpg"
base_url = "https://static.cornelsen.de/bgd/97/83/46/48/50/38/1/9783464850381_x1LIAB/preview/big/{}.jpg"
base_url = "https://static.cornelsen.de/bgd/97/83/06/06/27/81/3/9783060627813_x1LIAB/preview/big/{}.jpg" 
base_url = "https://klettbib.livebook.de/978-3-12-443550-7/preview/big/{}.jpg"
base_url = "https://www.ccbuchner.de/_files_media/livebook/4263/preview/big/{}.jpg"
base_url = "https://static.cornelsen.de/bgd/97/83/06/04/11/07/8/9783060411078_x1LIAB/preview/big/{}.jpg"
base_url = "https://static.cornelsen.de/bgd/97/83/06/01/48/69/1/9783060148691_x1LIAB/preview/big/{}.jpg"
base_url = "https://blickinsbuch.westermann.de/978-3-507-53254-0/content/pages/page{}.jpg" # incomplete
base_url = "https://static.cornelsen.de/bgd/97/83/06/06/73/46/9/9783060673469_x1LIAB/preview/big/328.jpg"

# Create a directory for downloaded images
download_dir = Path("Deutsch7")
last_page = 328  # Total number of pages to download

def download_images_and_zip():
    """
    Download images from Westermann website and create a zip file.
    """
    download_dir.mkdir(exist_ok=True)
    
    # List to keep track of successfully downloaded files
    downloaded_files = []
    
    print(f"Starting download of {last_page} pages...")
    
    # Download each page

    for page_num in range(1, last_page+1):  
        url = base_url.format(page_num)
        filename = f"page{page_num}.jpg"
        filepath = download_dir / filename
        
        try:
            print(f"Downloading page {page_num}/{last_page}...", end=" ")
            
            # Make request with timeout and user agent
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()  # Raises an HTTPError for bad responses
            
            # Save the image
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            downloaded_files.append(filepath)
            print("✓")
            
            # Small delay to be respectful to the server
            time.sleep(0.5)
            
        except requests.exceptions.RequestException as e:
            print(f"✗ Error: {e}")
            continue
        except Exception as e:
            print(f"✗ Unexpected error: {e}")
            continue
    
    print(f"\nDownload complete! Successfully downloaded {len(downloaded_files)} out of 204 pages.")
    

if __name__ == "__main__":
    # Check if required libraries are installed
    try:
        import requests
    except ImportError:
        print("Error: 'requests' library not found. Install it with: pip install requests")
        exit(1)

    print(base_url)
    print(download_dir)
    print(last_page)    
    download_images_and_zip()
