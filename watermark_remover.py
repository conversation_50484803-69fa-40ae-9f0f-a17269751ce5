import cv2
import numpy as np
from PIL import Image
from glob import glob
import matplotlib.pyplot as plt

def extract_watermark_template(template_image_path):
    """
    Extract the watermark template from a blank watermarked image
    
    Args:
        template_image_path: Path to the blank image with only watermark
    
    Returns:
        watermark_mask: Binary mask of the watermark
        watermark_alpha: Alpha channel representing watermark opacity
    """
    # Load the template image
    template = cv2.imread(template_image_path)
    if template is None:
        raise ValueError("Could not load template image")
    
    # Convert to grayscale
    gray_template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
    
    # Create watermark mask
    # Assume the background is white (255) and watermark creates darker areas
    # Adjust threshold based on your watermark
    _, watermark_mask = cv2.threshold(gray_template, 240, 255, cv2.THRESH_BINARY_INV)
    
    # Create alpha channel (opacity map)
    # Areas closer to white (255) have lower opacity, darker areas have higher opacity
    watermark_alpha = (255 - gray_template).astype(np.float32) / 255.0
    
    # Clean up the mask to remove noise
    kernel = np.ones((3,3), np.uint8)
    watermark_mask = cv2.morphologyEx(watermark_mask, cv2.MORPH_OPEN, kernel, iterations=1)
    watermark_mask = cv2.morphologyEx(watermark_mask, cv2.MORPH_CLOSE, kernel, iterations=2)
    
    return watermark_mask, watermark_alpha, template

def remove_watermark_with_template(target_image_path, watermark_mask, watermark_alpha, method='inpaint'):
    """
    Remove watermark from target image using the extracted template
    
    Args:
        target_image_path: Path to the image with watermark to be removed
        watermark_mask: Binary mask of the watermark
        watermark_alpha: Alpha channel of the watermark
        method: Removal method ('inpaint', 'alpha_blend', 'frequency')
    
    Returns:
        cleaned_image: Image with watermark removed
    """
    # Load target image
    target = cv2.imread(target_image_path)
    if target is None:
        raise ValueError("Could not load target image")
    
    # Ensure both images have the same dimensions
    if target.shape[:2] != watermark_mask.shape[:2]:
        # Resize watermark template to match target image
        watermark_mask = cv2.resize(watermark_mask, (target.shape[1], target.shape[0]))
        watermark_alpha = cv2.resize(watermark_alpha, (target.shape[1], target.shape[0]))
    
    if method == 'inpaint':
        # Method 1: Use inpainting on watermark areas
        result = cv2.inpaint(target, watermark_mask, 3, cv2.INPAINT_TELEA)
        
    elif method == 'alpha_blend':
        # Method 2: Reverse the alpha blending process
        result = target.copy().astype(np.float32)
        
        # For each channel, reverse the alpha blending
        # Assuming watermark was applied as: result = original * (1 - alpha) + watermark_color * alpha
        # We want to recover: original = (result - watermark_color * alpha) / (1 - alpha)
        
        for c in range(3):
            mask_3d = watermark_alpha > 0.01  # Only process areas with significant watermark
            
            # Estimate watermark color (assuming it's grayish)
            watermark_color = 128  # Adjust based on your watermark color
            
            # Reverse alpha blending
            channel = result[:, :, c]
            alpha_expanded = watermark_alpha
            
            # Avoid division by zero
            denominator = 1 - alpha_expanded
            denominator[denominator < 0.01] = 0.01
            
            # Reverse the blending
            recovered = (channel - watermark_color * alpha_expanded) / denominator
            
            # Apply only where watermark exists
            channel[mask_3d] = recovered[mask_3d]
            result[:, :, c] = channel
        
        # Clip values to valid range
        result = np.clip(result, 0, 255).astype(np.uint8)
        
    elif method == 'frequency':
        # Method 3: Frequency domain subtraction
        target_gray = cv2.cvtColor(target, cv2.COLOR_BGR2GRAY)
        
        # Apply FFT to both images
        f_target = np.fft.fft2(target_gray.astype(np.float32))
        f_watermark = np.fft.fft2(watermark_alpha * 255)
        
        # Subtract watermark in frequency domain
        f_result = f_target - 0.3 * f_watermark  # Adjust factor as needed
        
        # Convert back to spatial domain
        result_gray = np.real(np.fft.ifft2(f_result))
        result_gray = np.clip(result_gray, 0, 255).astype(np.uint8)
        
        # Convert back to color
        result = cv2.cvtColor(result_gray, cv2.COLOR_GRAY2BGR)
    
    elif method == 'adaptive':
        # Method 4: Adaptive restoration based on local statistics
        result = target.copy().astype(np.float32)
        
        # Create a slightly dilated mask for context
        kernel = np.ones((5,5), np.uint8)
        dilated_mask = cv2.dilate(watermark_mask, kernel, iterations=2)
        
        # For each watermark pixel, estimate the original value
        # using surrounding non-watermark pixels
        for y in range(target.shape[0]):
            for x in range(target.shape[1]):
                if watermark_mask[y, x] > 0:
                    # Get local neighborhood
                    y1, y2 = max(0, y-10), min(target.shape[0], y+11)
                    x1, x2 = max(0, x-10), min(target.shape[1], x+11)
                    
                    # Get pixels in neighborhood that are not watermarked
                    neighborhood = target[y1:y2, x1:x2]
                    neighborhood_mask = dilated_mask[y1:y2, x1:x2]
                    
                    for c in range(3):
                        channel_neighborhood = neighborhood[:, :, c]
                        valid_pixels = channel_neighborhood[neighborhood_mask == 0]
                        
                        if len(valid_pixels) > 0:
                            # Use median of valid pixels as estimate
                            result[y, x, c] = np.median(valid_pixels)
        
        result = result.astype(np.uint8)
    
    return result

def process_watermarked_image(template_path, target_path, output_path=None):
    """
    Complete pipeline to remove watermark using template
    
    Args:
        template_path: Path to blank watermarked template
        target_path: Path to target image with watermark
        output_path: Path to save cleaned image (optional)
    
    Returns:
        Dictionary with results from different methods
    """
    
    # Extract watermark template
    print("Extracting watermark template...")
    watermark_mask, watermark_alpha, template_img = extract_watermark_template(template_path)
    
    # Try different removal methods
    methods = ['inpaint', 'alpha_blend', 'adaptive']
    methods = ['inpaint']
    results = {}
    
    for method in methods:
        print(f"Trying {method} method...")
        try:
            cleaned = remove_watermark_with_template(target_path, watermark_mask, 
                                                   watermark_alpha, method=method)
            results[method] = cleaned
            
            # Save result if output path is provided
            if output_path:
                method_output = output_path.replace('.', f'_{method}.')
                cv2.imwrite(method_output, cleaned)
                print(f"Saved {method} result to {method_output}")
                
        except Exception as e:
            print(f"Method {method} failed: {e}")
            results[method] = None
    
    return results, watermark_mask, watermark_alpha

def display_comparison(template_path, target_path, results, watermark_mask):
    """
    Display original, template, and cleaned images for comparison
    """
    # Load original images
    template = cv2.imread(template_path)
    target = cv2.imread(target_path)
    
    # Convert to RGB for matplotlib
    template_rgb = cv2.cvtColor(template, cv2.COLOR_BGR2RGB)
    target_rgb = cv2.cvtColor(target, cv2.COLOR_BGR2RGB)
    
    # Create subplot
    methods = [m for m in results.keys() if results[m] is not None]
    n_methods = len(methods)
    
    fig, axes = plt.subplots(2, max(3, n_methods), figsize=(15, 10))
    
    # First row: original images and mask
    axes[0, 0].imshow(template_rgb)
    axes[0, 0].set_title('Watermark Template')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(target_rgb)
    axes[0, 1].set_title('Target Image')
    axes[0, 1].axis('off')
    
    axes[0, 2].imshow(watermark_mask, cmap='gray')
    axes[0, 2].set_title('Extracted Mask')
    axes[0, 2].axis('off')
    
    # Second row: results
    for i, method in enumerate(methods):
        if results[method] is not None:
            result_rgb = cv2.cvtColor(results[method], cv2.COLOR_BGR2RGB)
            axes[1, i].imshow(result_rgb)
            axes[1, i].set_title(f'{method.capitalize()} Result')
            axes[1, i].axis('off')
    
    plt.tight_layout()
    plt.show()

# Main execution function
def clean_image_with_template(template_path, target_path, output_path=None, display=False):
    """
    Main function to clean an image using watermark template
    
    Args:
        template_path: Path to the blank watermarked image (your image 002)
        target_path: Path to the image to be cleaned (your image 003)
        output_path: Path to save the cleaned image
        display: Whether to display comparison plots
    """
    
    try:
        # Process the image
        results, watermark_mask, watermark_alpha = process_watermarked_image(
            template_path, target_path, output_path
        )
        
        # Display results if requested
        if display:
            display_comparison(template_path, target_path, results, watermark_mask)
        
        # Return the best result (you can modify this logic)
        best_method = 'inpaint'  # Usually works best for text watermarks
        if best_method in results and results[best_method] is not None:
            return results[best_method]
        else:
            # Return first available result
            for method, result in results.items():
                if result is not None:
                    return result
        
        return None
        
    except Exception as e:
        print(f"Error processing images: {e}")
        return None

# Example usage
if __name__ == "__main__":
    # Replace these with your actual file paths
    template_path = "GeschichteUndGeschehen10/template.jpg"  # Your blank watermarked image (002)
    for target_path in glob("GeschichteUndGeschehen10/page*.jpg"):
    
        output_path = target_path + "_cleaned.jpg"  # Where to save the result
        
        # Clean the image
        cleaned_image = clean_image_with_template(template_path, target_path, output_path)
    
    if cleaned_image is not None:
        print("Successfully removed watermark!")
    else:
        print("Failed to remove watermark.")
